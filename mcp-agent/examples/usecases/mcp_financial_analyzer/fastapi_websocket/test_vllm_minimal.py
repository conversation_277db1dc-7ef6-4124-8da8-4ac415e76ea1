#!/usr/bin/env python3
"""
Minimal test to understand VLLM server behavior and find working configuration.
"""

import requests
import json
from openai import OpenAI
import asyncio


def test_vllm_endpoints():
    """Test various VLLM endpoints to understand server configuration."""
    base_url = "http://192.168.1.54:38701"
    
    print("🔍 Testing VLLM Server Endpoints")
    print("=" * 50)
    
    endpoints_to_test = [
        "/health",
        "/v1/models", 
        "/models",
        "/v1/completions",
        "/completions",
        "/v1/chat/completions",
        "/chat/completions",
        "/version",
        "/info",
        "/status"
    ]
    
    for endpoint in endpoints_to_test:
        url = f"{base_url}{endpoint}"
        print(f"\nTesting: {url}")
        
        try:
            response = requests.get(url, timeout=5)
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  Response: {json.dumps(data, indent=2)[:200]}...")
                except:
                    print(f"  Response: {response.text[:200]}...")
            else:
                print(f"  Error: {response.text[:100]}...")
                
        except Exception as e:
            print(f"  Exception: {e}")


def test_completions_without_model():
    """Test completions endpoint without specifying a model."""
    print("\n🧪 Testing Completions Without Model")
    print("=" * 40)
    
    api_url = "http://192.168.1.54:38701/v1"
    
    # Test with requests directly
    print("1. Testing with requests library...")
    try:
        payload = {
            "messages": [
                {"role": "user", "content": "Hello"}
            ],
            "max_tokens": 10,
            "temperature": 0.1
        }
        
        response = requests.post(
            f"{api_url}/chat/completions",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Success! Response: {json.dumps(data, indent=2)[:300]}...")
            
            # Extract model name if available
            if 'model' in data:
                print(f"   Model used: {data['model']}")
                return data['model']
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    # Test with OpenAI client without model
    print("\n2. Testing with OpenAI client (no model)...")
    try:
        client = OpenAI(api_key="EMPTY", base_url=api_url)
        
        # Try with empty model string
        response = client.chat.completions.create(
            model="",
            messages=[{"role": "user", "content": "Hello"}],
            max_tokens=10
        )
        
        if response.choices:
            print(f"   Success! Response: {response.choices[0].message.content}")
            print(f"   Model: {response.model}")
            return response.model
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    return None


def test_with_discovered_model(model_name):
    """Test streaming with discovered model."""
    if not model_name:
        print("\n❌ No model discovered, cannot test streaming")
        return False
    
    print(f"\n🚀 Testing Streaming with Model: {model_name}")
    print("=" * 50)
    
    api_url = "http://192.168.1.54:38701/v1"
    client = OpenAI(api_key="EMPTY", base_url=api_url)
    
    try:
        print("Testing streaming...")
        stream = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "You are a helpful financial analyst."},
                {"role": "user", "content": "Analyze Apple Inc. stock briefly."}
            ],
            max_tokens=100,
            temperature=0.7,
            stream=True
        )
        
        chunks = []
        print("📦 Streaming chunks:")
        
        for i, chunk in enumerate(stream):
            if chunk.choices and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                chunks.append(content)
                print(f"  {i+1:2d}: {repr(content)}")
                
                if len(chunks) >= 10:  # Limit for testing
                    break
        
        print(f"\n✅ Streaming successful!")
        print(f"   Total chunks: {len(chunks)}")
        print(f"   Full response: {''.join(chunks)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Streaming failed: {e}")
        return False


async def test_with_streaming_llm(model_name):
    """Test with our StreamingVLLMLLM implementation."""
    if not model_name:
        print("\n❌ No model available for StreamingVLLMLLM test")
        return False
    
    print(f"\n🎯 Testing StreamingVLLMLLM with: {model_name}")
    print("=" * 50)
    
    from session_manager import StreamingVLLMLLM
    from unittest.mock import Mock
    
    # Create mock LLM
    mock_llm = Mock()
    mock_llm.vllm_api_base = "http://192.168.1.54:38701/v1"
    mock_llm.vllm_api_key = "EMPTY"
    mock_llm.instruction = "You are a financial analyst."
    mock_llm.generate_str = lambda msg, params=None: f"Mock fallback: {msg}"
    
    streaming_llm = StreamingVLLMLLM(mock_llm)
    
    chunks_received = []
    
    async def collect_chunks(chunk: str):
        chunks_received.append(chunk)
        print(f"📦 Chunk {len(chunks_received)}: {chunk[:80]}{'...' if len(chunk) > 80 else ''}")
    
    try:
        result = await streaming_llm.generate_str_streaming(
            message="Analyze Apple Inc. stock performance briefly.",
            stream_callback=collect_chunks,
            model=model_name
        )
        
        # Check if we got real streaming
        has_real_content = any(
            len(chunk.strip()) > 20 and 
            not any(keyword in chunk.lower() for keyword in ["fallback", "failed", "error"])
            for chunk in chunks_received[1:]  # Skip initialization message
        )
        
        print(f"\n📊 StreamingVLLMLLM Results:")
        print(f"   Chunks: {len(chunks_received)}")
        print(f"   Result length: {len(result) if result else 0}")
        print(f"   Has real content: {'✅' if has_real_content else '❌'}")
        
        if has_real_content:
            print(f"🎉 SUCCESS: StreamingVLLMLLM is working with real VLLM streaming!")
            return True
        else:
            print(f"⚠️ StreamingVLLMLLM working but using fallback")
            return False
            
    except Exception as e:
        print(f"❌ StreamingVLLMLLM failed: {e}")
        return False


async def main():
    """Main testing function."""
    print("🚀 Minimal VLLM Server Test")
    print("=" * 60)
    
    # Test endpoints
    test_vllm_endpoints()
    
    # Try to find working model
    discovered_model = test_completions_without_model()
    
    if discovered_model:
        print(f"\n🎯 Discovered working model: {discovered_model}")
        
        # Test direct streaming
        streaming_works = test_with_discovered_model(discovered_model)
        
        if streaming_works:
            # Test with our implementation
            our_streaming_works = await test_with_streaming_llm(discovered_model)
            
            if our_streaming_works:
                print(f"\n🎉 COMPLETE SUCCESS!")
                print(f"   VLLM server is working")
                print(f"   Model: {discovered_model}")
                print(f"   Direct streaming: ✅")
                print(f"   StreamingVLLMLLM: ✅")
                
                print(f"\n📝 Configuration to use:")
                print(f"   vllm:")
                print(f"     api_base: \"http://192.168.1.54:38701/v1\"")
                print(f"     default_model: \"{discovered_model}\"")
                print(f"     api_key: \"EMPTY\"")
                
                return True
    
    print(f"\n❌ Could not establish working VLLM configuration")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
