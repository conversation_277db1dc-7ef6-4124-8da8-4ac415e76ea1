#!/usr/bin/env python3
"""
Debug script to investigate VLLM API endpoints and available models.
"""

import requests
import json
import asyncio
from openai import OpenAI


async def debug_vllm_api():
    """Debug VLLM API to understand available endpoints and models."""
    base_url = "http://************:38701"
    api_url = f"{base_url}/v1"
    
    print("🔍 Debugging VLLM API")
    print("=" * 50)
    print(f"Base URL: {base_url}")
    print(f"API URL: {api_url}")
    
    # Test 1: Check health endpoint
    print("\n1. Testing health endpoint...")
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   Status: {health_response.status_code}")
        if health_response.status_code == 200:
            print(f"   Response: {health_response.text}")
        else:
            print(f"   Error: {health_response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 2: Check models endpoint
    print("\n2. Testing models endpoint...")
    try:
        models_response = requests.get(f"{api_url}/models", timeout=5)
        print(f"   Status: {models_response.status_code}")
        if models_response.status_code == 200:
            models_data = models_response.json()
            print(f"   Available models:")
            for model in models_data.get('data', []):
                print(f"     - {model.get('id', 'Unknown')}")
        else:
            print(f"   Error: {models_response.text}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Test completions endpoint with different models
    print("\n3. Testing completions endpoint...")
    
    # Try to get available models first
    available_models = []
    try:
        models_response = requests.get(f"{api_url}/models", timeout=5)
        if models_response.status_code == 200:
            models_data = models_response.json()
            available_models = [model.get('id') for model in models_data.get('data', [])]
    except:
        pass
    
    # Default models to try if we can't get the list
    if not available_models:
        available_models = [
            "Qwen/Qwen3-32B",
            "Qwen/Qwen3-8B", 
            "Qwen/Qwen2.5-32B-Instruct",
            "meta-llama/Llama-3.1-8B-Instruct"
        ]
    
    print(f"   Testing with models: {available_models[:3]}")  # Test first 3
    
    for model in available_models[:3]:
        print(f"\n   Testing model: {model}")
        
        try:
            # Test with OpenAI client
            client = OpenAI(
                api_key="EMPTY",
                base_url=api_url
            )
            
            # Test non-streaming first
            print("     Testing non-streaming...")
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant."},
                    {"role": "user", "content": "Say hello in one sentence."}
                ],
                max_tokens=50,
                temperature=0.7
            )
            
            if response.choices:
                content = response.choices[0].message.content
                print(f"     ✅ Non-streaming works: {content[:50]}...")
                
                # Test streaming
                print("     Testing streaming...")
                stream = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": "Count from 1 to 5, one number per response."}
                    ],
                    max_tokens=50,
                    temperature=0.7,
                    stream=True
                )
                
                chunks = []
                for chunk in stream:
                    if chunk.choices and chunk.choices[0].delta.content:
                        chunks.append(chunk.choices[0].delta.content)
                        if len(chunks) >= 5:  # Limit chunks for testing
                            break
                
                if chunks:
                    print(f"     ✅ Streaming works: {len(chunks)} chunks received")
                    print(f"     First few chunks: {chunks[:3]}")
                    return model  # Return working model
                else:
                    print("     ❌ Streaming failed: No chunks received")
            else:
                print("     ❌ Non-streaming failed: No response content")
                
        except Exception as e:
            print(f"     ❌ Error with {model}: {e}")
    
    return None


async def test_working_model_with_streaming_llm(working_model: str):
    """Test the working model with our StreamingVLLMLLM implementation."""
    print(f"\n🧪 Testing StreamingVLLMLLM with working model: {working_model}")
    print("-" * 60)
    
    from session_manager import StreamingVLLMLLM
    from unittest.mock import Mock
    
    # Create mock LLM with working model
    mock_llm = Mock()
    mock_llm.vllm_api_base = "http://************:38701/v1"
    mock_llm.vllm_api_key = "EMPTY"
    mock_llm.instruction = "You are a financial analyst."
    mock_llm.generate_str = lambda msg, params=None: f"Fallback response for: {msg}"
    
    streaming_llm = StreamingVLLMLLM(mock_llm)
    
    # Test streaming with working model
    chunks_received = []
    
    async def collect_chunks(chunk: str):
        chunks_received.append(chunk)
        print(f"📦 Chunk {len(chunks_received)}: {chunk[:80]}{'...' if len(chunk) > 80 else ''}")
    
    try:
        result = await streaming_llm.generate_str_streaming(
            message="Analyze Apple Inc. stock performance briefly.",
            stream_callback=collect_chunks,
            model=working_model
        )
        
        print(f"\n✅ Streaming test completed!")
        print(f"   Chunks received: {len(chunks_received)}")
        print(f"   Result length: {len(result) if result else 0}")
        print(f"   Contains fallback indicators: {any('fallback' in chunk.lower() or 'failed' in chunk.lower() for chunk in chunks_received)}")
        
        # Check if we got real streaming
        is_real_streaming = (
            len(chunks_received) > 3 and
            not any("fallback" in chunk.lower() or "failed" in chunk.lower() for chunk in chunks_received[:3]) and
            any(len(chunk.strip()) > 10 for chunk in chunks_received[1:])
        )
        
        print(f"   Real streaming detected: {'✅' if is_real_streaming else '❌'}")
        
        return is_real_streaming
        
    except Exception as e:
        print(f"❌ StreamingVLLMLLM test failed: {e}")
        return False


async def main():
    """Main debug function."""
    print("🚀 VLLM API Debug and Validation")
    print("=" * 60)
    
    # Debug API and find working model
    working_model = await debug_vllm_api()
    
    if working_model:
        print(f"\n🎯 Found working model: {working_model}")
        
        # Test with our StreamingVLLMLLM
        streaming_success = await test_working_model_with_streaming_llm(working_model)
        
        if streaming_success:
            print(f"\n🎉 SUCCESS: Real VLLM streaming is working with model {working_model}!")
            
            # Update configuration recommendation
            print(f"\n📝 Configuration Recommendation:")
            print(f"   Update mcp_agent.config.yaml:")
            print(f"   vllm:")
            print(f"     api_base: \"http://************:38701/v1\"")
            print(f"     default_model: \"{working_model}\"")
            print(f"     api_key: \"EMPTY\"")
            
        else:
            print(f"\n⚠️ Model {working_model} works with direct API but not with StreamingVLLMLLM")
    else:
        print(f"\n❌ No working models found. Check VLLM server configuration.")
    
    return working_model is not None


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
