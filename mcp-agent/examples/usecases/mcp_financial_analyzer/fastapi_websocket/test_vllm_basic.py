#!/usr/bin/env python3
"""
Comprehensive VLLM Streaming Integration Test Suite
==================================================
Validates the VLLM streaming implementation with real API integration
across all three stages of the financial analysis workflow.

Requirements:
- VLLM server running at http://192.168.1.54:38701/v1
- Real API integration (NO MOCKS)
- Tests for Research, Analysis, and Report stages
- Streaming validation and error handling
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
from datetime import datetime
import aiohttp
import pytest

# Add the parent directory to the path to import session_manager
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from session_manager import StreamingVLLMLLM
from mcp_agent.workflows.llm.augmented_llm_vllm import VLLMAugmentedLLM
from mcp_agent.workflows.llm.augmented_llm import RequestParams

# VLLM API Configuration - REAL ENDPOINT
VLLM_API_BASE = "http://192.168.1.54:38701/v1"
VLLM_API_KEY = "EMPTY"
VLLM_MODEL = "Qwen/Qwen3-32B"

# Test company for financial analysis
TEST_COMPANY = "Apple Inc."

class VLLMStreamingTestSuite:
    """Comprehensive test suite for VLLM streaming implementation."""

    def __init__(self):
        self.test_results = []
        self.streaming_chunks = []
        self.api_connectivity_verified = False

    async def verify_vllm_connectivity(self) -> bool:
        """Verify VLLM server is accessible and responsive."""
        print("🔍 Verifying VLLM server connectivity...")

        try:
            async with aiohttp.ClientSession() as session:
                # Test basic connectivity
                async with session.get(f"{VLLM_API_BASE}/models", timeout=10) as response:
                    if response.status == 200:
                        models_data = await response.json()
                        print(f"✅ VLLM server accessible. Available models: {len(models_data.get('data', []))}")
                        self.api_connectivity_verified = True
                        return True
                    else:
                        print(f"❌ VLLM server returned status {response.status}")
                        return False
        except Exception as e:
            print(f"❌ Failed to connect to VLLM server: {e}")
            return False

    async def create_real_vllm_llm(self) -> VLLMAugmentedLLM:
        """Create a real VLLMAugmentedLLM instance with proper configuration."""
        # Create a mock context with VLLM configuration
        class MockConfig:
            def __init__(self):
                self.vllm = MockVLLMConfig()

        class MockVLLMConfig:
            def __init__(self):
                self.api_base = VLLM_API_BASE
                self.api_key = VLLM_API_KEY

        class MockContext:
            def __init__(self):
                self.config = MockConfig()

        # Create VLLMAugmentedLLM with real configuration
        vllm_llm = VLLMAugmentedLLM(
            name="test_vllm",
            default_model=VLLM_MODEL,
            context=MockContext()
        )

        # Set instruction for financial analysis
        vllm_llm.instruction = f"You are a professional financial analyst specializing in {TEST_COMPANY} analysis."

        return vllm_llm

    async def test_research_stage_streaming(self) -> bool:
        """Test VLLM streaming for the research stage of financial analysis."""
        print("\n🔬 Testing Research Stage Streaming...")

        try:
            # Create real VLLM LLM
            vllm_llm = await self.create_real_vllm_llm()
            streaming_llm = StreamingVLLMLLM(vllm_llm)

            # Research stage message
            research_message = f"""Conduct financial research on {TEST_COMPANY}. Provide:
1. Current stock price and recent movement
2. Latest quarterly earnings results
3. Recent significant news or developments
4. Market sentiment and analyst recommendations

Keep the response concise but informative."""

            # Track streaming chunks
            research_chunks = []

            async def research_callback(chunk: str):
                research_chunks.append(chunk)
                print(f"📊 Research chunk: {chunk[:80]}...")

            # Execute streaming research
            start_time = time.time()
            result = await streaming_llm.generate_str_streaming(
                message=research_message,
                request_params=RequestParams(model=VLLM_MODEL),
                stream_callback=research_callback
            )
            end_time = time.time()

            # Validate results
            success = self._validate_research_response(result, research_chunks, end_time - start_time)
            self.test_results.append({
                "stage": "research",
                "success": success,
                "chunks_received": len(research_chunks),
                "response_length": len(result),
                "duration": end_time - start_time
            })

            return success

        except Exception as e:
            print(f"❌ Research stage test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def test_analysis_stage_streaming(self) -> bool:
        """Test VLLM streaming for the analysis stage of financial analysis."""
        print("\n📈 Testing Analysis Stage Streaming...")

        try:
            # Create real VLLM LLM
            vllm_llm = await self.create_real_vllm_llm()
            streaming_llm = StreamingVLLMLLM(vllm_llm)

            # Mock research data for analysis
            research_data = f"""Research Data for {TEST_COMPANY}:
- Stock Price: $150.25 (up 2.3% today)
- Q4 Earnings: Beat expectations by $0.15 per share
- Revenue: $95.2B (up 8% YoY)
- Recent News: New product launch announced
- Analyst Rating: Strong Buy (average target $165)"""

            analysis_message = f"""Analyze the following financial data for {TEST_COMPANY}:

{research_data}

Provide detailed analysis including:
1. Performance assessment vs expectations
2. Key strengths and potential concerns
3. Financial metrics evaluation
4. Investment recommendation with rationale"""

            # Track streaming chunks
            analysis_chunks = []

            async def analysis_callback(chunk: str):
                analysis_chunks.append(chunk)
                print(f"📊 Analysis chunk: {chunk[:80]}...")

            # Execute streaming analysis
            start_time = time.time()
            result = await streaming_llm.generate_str_streaming(
                message=analysis_message,
                request_params=RequestParams(model=VLLM_MODEL),
                stream_callback=analysis_callback
            )
            end_time = time.time()

            # Validate results
            success = self._validate_analysis_response(result, analysis_chunks, end_time - start_time)
            self.test_results.append({
                "stage": "analysis",
                "success": success,
                "chunks_received": len(analysis_chunks),
                "response_length": len(result),
                "duration": end_time - start_time
            })

            return success

        except Exception as e:
            print(f"❌ Analysis stage test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    async def test_report_stage_streaming(self) -> bool:
        """Test VLLM streaming for the report generation stage."""
        print("\n📝 Testing Report Stage Streaming...")

        try:
            # Create real VLLM LLM
            vllm_llm = await self.create_real_vllm_llm()
            streaming_llm = StreamingVLLMLLM(vllm_llm)

            # Mock analysis data for report generation
            analysis_data = f"""Financial Analysis Summary for {TEST_COMPANY}:

Performance Assessment:
- Stock outperformed market expectations with 2.3% daily gain
- Q4 earnings exceeded analyst estimates by $0.15 per share
- Revenue growth of 8% YoY demonstrates strong business momentum

Key Strengths:
- Consistent revenue growth across all segments
- Strong cash flow generation
- Successful product innovation pipeline

Potential Concerns:
- Market saturation in core segments
- Increased competition pressure
- Supply chain dependencies

Investment Recommendation: Strong Buy
Target Price: $165 (10% upside potential)"""

            report_message = f"""Generate a professional financial report for {TEST_COMPANY} based on this analysis:

{analysis_data}

Create a comprehensive report with:
1. Executive Summary
2. Financial Performance Overview
3. Key Metrics and Ratios
4. Risk Assessment
5. Investment Recommendation
6. Conclusion

Format as a professional markdown report with proper sections and formatting."""

            # Track streaming chunks
            report_chunks = []

            async def report_callback(chunk: str):
                report_chunks.append(chunk)
                print(f"📄 Report chunk: {chunk[:80]}...")

            # Execute streaming report generation
            start_time = time.time()
            result = await streaming_llm.generate_str_streaming(
                message=report_message,
                request_params=RequestParams(model=VLLM_MODEL),
                stream_callback=report_callback
            )
            end_time = time.time()

            # Validate results
            success = self._validate_report_response(result, report_chunks, end_time - start_time)
            self.test_results.append({
                "stage": "report",
                "success": success,
                "chunks_received": len(report_chunks),
                "response_length": len(result),
                "duration": end_time - start_time
            })

            return success

        except Exception as e:
            print(f"❌ Report stage test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _validate_research_response(self, response: str, chunks: List[str], duration: float) -> bool:
        """Validate research stage response quality and streaming behavior."""
        print(f"🔍 Validating research response ({len(response)} chars, {len(chunks)} chunks, {duration:.2f}s)")

        # Check response content quality
        research_keywords = ["stock", "price", "earnings", "revenue", "market", "analyst"]
        keyword_matches = sum(1 for keyword in research_keywords if keyword.lower() in response.lower())

        # Streaming validation
        streaming_valid = len(chunks) > 1  # Should receive multiple chunks
        content_valid = len(response) > 100  # Should have substantial content
        keyword_valid = keyword_matches >= 3  # Should contain financial keywords
        timing_valid = duration > 0.5  # Should take reasonable time for real API call

        success = streaming_valid and content_valid and keyword_valid and timing_valid

        print(f"  📊 Streaming chunks: {len(chunks)} {'✅' if streaming_valid else '❌'}")
        print(f"  📝 Content length: {len(response)} {'✅' if content_valid else '❌'}")
        print(f"  🔑 Keywords found: {keyword_matches}/6 {'✅' if keyword_valid else '❌'}")
        print(f"  ⏱️ Duration: {duration:.2f}s {'✅' if timing_valid else '❌'}")

        return success

    def _validate_analysis_response(self, response: str, chunks: List[str], duration: float) -> bool:
        """Validate analysis stage response quality and streaming behavior."""
        print(f"🔍 Validating analysis response ({len(response)} chars, {len(chunks)} chunks, {duration:.2f}s)")

        # Check response content quality
        analysis_keywords = ["performance", "strength", "concern", "recommendation", "analysis", "metrics"]
        keyword_matches = sum(1 for keyword in analysis_keywords if keyword.lower() in response.lower())

        # Streaming validation
        streaming_valid = len(chunks) > 1
        content_valid = len(response) > 150  # Analysis should be more detailed
        keyword_valid = keyword_matches >= 3
        timing_valid = duration > 0.5

        success = streaming_valid and content_valid and keyword_valid and timing_valid

        print(f"  📊 Streaming chunks: {len(chunks)} {'✅' if streaming_valid else '❌'}")
        print(f"  📝 Content length: {len(response)} {'✅' if content_valid else '❌'}")
        print(f"  🔑 Keywords found: {keyword_matches}/6 {'✅' if keyword_valid else '❌'}")
        print(f"  ⏱️ Duration: {duration:.2f}s {'✅' if timing_valid else '❌'}")

        return success

    def _validate_report_response(self, response: str, chunks: List[str], duration: float) -> bool:
        """Validate report stage response quality and streaming behavior."""
        print(f"🔍 Validating report response ({len(response)} chars, {len(chunks)} chunks, {duration:.2f}s)")

        # Check response content quality
        report_keywords = ["executive", "summary", "financial", "performance", "recommendation", "conclusion"]
        keyword_matches = sum(1 for keyword in report_keywords if keyword.lower() in response.lower())

        # Check for markdown formatting
        markdown_elements = ["#", "##", "###", "*", "-", "**"]
        markdown_matches = sum(1 for element in markdown_elements if element in response)

        # Streaming validation
        streaming_valid = len(chunks) > 1
        content_valid = len(response) > 200  # Reports should be comprehensive
        keyword_valid = keyword_matches >= 4
        format_valid = markdown_matches >= 3  # Should have markdown formatting
        timing_valid = duration > 0.5

        success = streaming_valid and content_valid and keyword_valid and format_valid and timing_valid

        print(f"  📊 Streaming chunks: {len(chunks)} {'✅' if streaming_valid else '❌'}")
        print(f"  📝 Content length: {len(response)} {'✅' if content_valid else '❌'}")
        print(f"  🔑 Keywords found: {keyword_matches}/6 {'✅' if keyword_valid else '❌'}")
        print(f"  📋 Markdown elements: {markdown_matches} {'✅' if format_valid else '❌'}")
        print(f"  ⏱️ Duration: {duration:.2f}s {'✅' if timing_valid else '❌'}")

        return success

    async def test_error_handling(self) -> bool:
        """Test error handling for network issues and API errors."""
        print("\n⚠️ Testing Error Handling...")

        try:
            # Test with invalid endpoint
            print("  🔧 Testing invalid endpoint handling...")

            class MockInvalidConfig:
                def __init__(self):
                    self.vllm = MockInvalidVLLMConfig()

            class MockInvalidVLLMConfig:
                def __init__(self):
                    self.api_base = "http://invalid-endpoint:99999/v1"
                    self.api_key = "EMPTY"

            class MockInvalidContext:
                def __init__(self):
                    self.config = MockInvalidConfig()

            # Create VLLMAugmentedLLM with invalid configuration
            invalid_vllm_llm = VLLMAugmentedLLM(
                name="test_invalid_vllm",
                default_model=VLLM_MODEL,
                context=MockInvalidContext()
            )

            streaming_llm = StreamingVLLMLLM(invalid_vllm_llm)

            # Track error handling
            error_chunks = []

            async def error_callback(chunk: str):
                error_chunks.append(chunk)
                print(f"🚨 Error chunk: {chunk[:80]}...")

            # This should handle the error gracefully
            result = await streaming_llm.generate_str_streaming(
                message="Test error handling",
                request_params=RequestParams(model=VLLM_MODEL),
                stream_callback=error_callback
            )

            # Validate error handling
            error_handled = "error" in result.lower() or "failed" in result.lower()
            fallback_used = len(result) > 0  # Should have some fallback response

            print(f"  🛡️ Error handled gracefully: {'✅' if error_handled else '❌'}")
            print(f"  🔄 Fallback response provided: {'✅' if fallback_used else '❌'}")

            return error_handled and fallback_used

        except Exception as e:
            print(f"❌ Error handling test failed: {e}")
            return False

    async def test_streaming_interruption(self) -> bool:
        """Test handling of streaming interruptions."""
        print("\n🔄 Testing Streaming Interruption Handling...")

        try:
            # Create real VLLM LLM
            vllm_llm = await self.create_real_vllm_llm()
            streaming_llm = StreamingVLLMLLM(vllm_llm)

            # Track interruption handling
            interruption_chunks = []
            interrupt_after = 3  # Interrupt after 3 chunks

            async def interruption_callback(chunk: str):
                interruption_chunks.append(chunk)
                print(f"🔄 Chunk {len(interruption_chunks)}: {chunk[:50]}...")

                # Simulate interruption after a few chunks
                if len(interruption_chunks) >= interrupt_after:
                    raise asyncio.CancelledError("Simulated interruption")

            # Test interruption handling
            try:
                _ = await streaming_llm.generate_str_streaming(
                    message="Generate a long financial analysis that will be interrupted",
                    request_params=RequestParams(model=VLLM_MODEL),
                    stream_callback=interruption_callback
                )
                # If we get here, interruption wasn't triggered or was handled
                interruption_handled = True
            except asyncio.CancelledError:
                print("  🔄 Streaming interruption occurred as expected")
                interruption_handled = True
            except Exception as e:
                print(f"  ⚠️ Unexpected error during interruption test: {e}")
                interruption_handled = False

            chunks_received = len(interruption_chunks) > 0

            print(f"  🔄 Interruption handled: {'✅' if interruption_handled else '❌'}")
            print(f"  📦 Chunks received before interruption: {len(interruption_chunks)} {'✅' if chunks_received else '❌'}")

            return interruption_handled and chunks_received

        except Exception as e:
            print(f"❌ Streaming interruption test failed: {e}")
            return False

    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all comprehensive VLLM streaming tests."""
        print("🚀 VLLM Streaming Integration Test Suite")
        print("=" * 60)
        print(f"🎯 Target VLLM Server: {VLLM_API_BASE}")
        print(f"🤖 Model: {VLLM_MODEL}")
        print(f"🏢 Test Company: {TEST_COMPANY}")
        print("=" * 60)

        # Verify VLLM connectivity first
        if not await self.verify_vllm_connectivity():
            return {
                "overall_success": False,
                "error": "VLLM server not accessible",
                "tests_run": 0,
                "tests_passed": 0
            }

        # Run all test stages
        test_methods = [
            ("Research Stage", self.test_research_stage_streaming),
            ("Analysis Stage", self.test_analysis_stage_streaming),
            ("Report Stage", self.test_report_stage_streaming),
            ("Error Handling", self.test_error_handling),
            ("Streaming Interruption", self.test_streaming_interruption)
        ]

        passed_tests = 0
        total_tests = len(test_methods)

        for test_name, test_method in test_methods:
            print(f"\n{'='*20} {test_name} {'='*20}")
            try:
                success = await test_method()
                if success:
                    passed_tests += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"💥 {test_name} CRASHED: {e}")

        # Generate test summary
        overall_success = passed_tests == total_tests

        print(f"\n{'='*60}")
        print("📊 TEST SUMMARY")
        print(f"{'='*60}")
        print(f"🎯 Tests Run: {total_tests}")
        print(f"✅ Tests Passed: {passed_tests}")
        print(f"❌ Tests Failed: {total_tests - passed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        if self.test_results:
            print(f"\n📋 DETAILED RESULTS:")
            for result in self.test_results:
                stage = result['stage'].title()
                status = "✅ PASS" if result['success'] else "❌ FAIL"
                chunks = result['chunks_received']
                length = result['response_length']
                duration = result['duration']
                print(f"  {stage}: {status} | {chunks} chunks | {length} chars | {duration:.2f}s")

        if overall_success:
            print(f"\n🎉 ALL TESTS PASSED! VLLM streaming is working correctly.")
        else:
            print(f"\n⚠️ SOME TESTS FAILED. Check the detailed results above.")

        return {
            "overall_success": overall_success,
            "tests_run": total_tests,
            "tests_passed": passed_tests,
            "test_results": self.test_results,
            "vllm_endpoint": VLLM_API_BASE,
            "model": VLLM_MODEL,
            "test_company": TEST_COMPANY
        }


# Standalone test functions for pytest compatibility
async def test_vllm_connectivity():
    """Test VLLM server connectivity."""
    suite = VLLMStreamingTestSuite()
    assert await suite.verify_vllm_connectivity(), "VLLM server not accessible"


async def test_research_streaming():
    """Test research stage streaming."""
    suite = VLLMStreamingTestSuite()
    if not await suite.verify_vllm_connectivity():
        pytest.skip("VLLM server not accessible")

    assert await suite.test_research_stage_streaming(), "Research stage streaming failed"


async def test_analysis_streaming():
    """Test analysis stage streaming."""
    suite = VLLMStreamingTestSuite()
    if not await suite.verify_vllm_connectivity():
        pytest.skip("VLLM server not accessible")

    assert await suite.test_analysis_stage_streaming(), "Analysis stage streaming failed"


async def test_report_streaming():
    """Test report stage streaming."""
    suite = VLLMStreamingTestSuite()
    if not await suite.verify_vllm_connectivity():
        pytest.skip("VLLM server not accessible")

    assert await suite.test_report_stage_streaming(), "Report stage streaming failed"


async def test_error_handling():
    """Test error handling capabilities."""
    suite = VLLMStreamingTestSuite()
    assert await suite.test_error_handling(), "Error handling test failed"


async def test_streaming_interruption():
    """Test streaming interruption handling."""
    suite = VLLMStreamingTestSuite()
    if not await suite.verify_vllm_connectivity():
        pytest.skip("VLLM server not accessible")

    assert await suite.test_streaming_interruption(), "Streaming interruption test failed"


async def main():
    """Main test execution function."""
    suite = VLLMStreamingTestSuite()
    results = await suite.run_comprehensive_tests()

    # Save results to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"vllm_streaming_test_results_{timestamp}.json"

    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    print(f"\n💾 Test results saved to: {results_file}")

    return results["overall_success"]


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
