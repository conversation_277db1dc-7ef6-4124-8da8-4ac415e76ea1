#!/usr/bin/env python3
"""
Basic test to verify StreamingVLLMLLM implementation works.
"""

import asyncio
from unittest.mock import Mock, AsyncMock
from session_manager import StreamingVLLMLLM

async def test_basic_streaming():
    """Test basic StreamingVLLMLLM functionality."""
    print("🧪 Testing StreamingVLLMLLM basic functionality...")
    
    # Create a mock VLLM LLM
    mock_vllm_llm = Mock()
    mock_vllm_llm.instruction = "Test instruction"
    mock_vllm_llm.vllm_api_base = "http://test:8000/v1"
    mock_vllm_llm.vllm_api_key = "test-key"
    mock_vllm_llm.generate_str = AsyncMock(return_value="Fallback response")
    
    # Create StreamingVLLMLLM instance
    try:
        streaming_llm = StreamingVLLMLLM(mock_vllm_llm)
        print("✅ StreamingVLLMLLM instantiated successfully")
        
        # Check if it has the required method
        assert hasattr(streaming_llm, 'generate_str_streaming')
        print("✅ generate_str_streaming method exists")
        
        # Test with mock callback (this will fail to connect to VLLM but should fallback gracefully)
        chunks_received = []
        
        async def test_callback(chunk):
            chunks_received.append(chunk)
            print(f"📦 Received chunk: {chunk[:50]}...")
        
        result = await streaming_llm.generate_str_streaming(
            message="Test message",
            stream_callback=test_callback
        )
        
        print(f"✅ Streaming completed. Chunks received: {len(chunks_received)}")
        print(f"✅ Final result: {result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run basic tests."""
    print("🚀 Basic VLLM Streaming Test")
    print("=" * 50)
    
    success = await test_basic_streaming()
    
    if success:
        print("\n🎉 Basic test passed! StreamingVLLMLLM is working.")
    else:
        print("\n❌ Basic test failed. Check implementation.")
    
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
