#!/usr/bin/env python3
"""
Manual test to verify VLLM streaming works in the WebSocket interface.
This test can be run to manually verify the implementation works.
"""

import asyncio
import json
import websockets
import time
from urllib.parse import quote


async def test_vllm_streaming():
    """Test VLLM streaming through WebSocket interface."""
    print("🚀 Manual VLLM Streaming Test")
    print("=" * 50)
    
    # Test configuration
    host = "localhost"
    port = 8000
    company = "Apple Inc."
    user_id = f"manual_test_{int(time.time())}"
    
    # Test different endpoints
    endpoints = ["research", "analyze", "report"]
    
    for endpoint in endpoints:
        print(f"\n🧪 Testing {endpoint.upper()} endpoint with VLLM streaming")
        print("-" * 40)
        
        uri = f"ws://{host}:{port}/ws/{endpoint}/{user_id}?company={quote(company)}"
        print(f"🔗 Connecting to: {uri}")
        
        try:
            async with websockets.connect(uri) as websocket:
                print("✅ Connected successfully")
                
                # Receive welcome message
                welcome = await websocket.recv()
                welcome_data = json.loads(welcome)
                print(f"👋 Welcome: {welcome_data['message']}")
                
                # Send streaming request
                test_message = f"Analyze {company} using VLLM streaming"
                print(f"📤 Sending: {test_message}")
                
                await websocket.send(json.dumps({
                    "message": test_message,
                    "streaming": True
                }))
                
                # Collect streaming response
                chunks_received = 0
                stream_started = False
                stream_ended = False
                start_time = time.time()
                
                print("📦 Streaming response:")
                print("-" * 30)
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(response)
                        
                        if data["type"] == "stream_start":
                            stream_started = True
                            print("🟢 Stream started")
                            
                        elif data["type"] == "stream_chunk":
                            chunks_received += 1
                            chunk = data["message"]
                            # Print first 100 chars of each chunk
                            print(f"   {chunks_received:2d}: {chunk[:100]}{'...' if len(chunk) > 100 else ''}")
                            
                        elif data["type"] == "stream_end":
                            stream_ended = True
                            print("🔴 Stream ended")
                            break
                            
                        elif data["type"] == "error":
                            print(f"❌ Error: {data['message']}")
                            break
                            
                    except asyncio.TimeoutError:
                        print("⏰ Timeout waiting for response")
                        break
                
                end_time = time.time()
                duration = end_time - start_time
                
                # Summary
                print("-" * 30)
                print(f"📊 Summary for {endpoint}:")
                print(f"   Stream started: {'✅' if stream_started else '❌'}")
                print(f"   Stream ended: {'✅' if stream_ended else '❌'}")
                print(f"   Chunks received: {chunks_received}")
                print(f"   Duration: {duration:.2f}s")
                print(f"   Avg chunk rate: {chunks_received/duration:.1f} chunks/sec" if duration > 0 else "")
                
                if chunks_received > 0 and stream_started and stream_ended:
                    print(f"✅ {endpoint.upper()} endpoint: PASSED")
                else:
                    print(f"❌ {endpoint.upper()} endpoint: FAILED")
                    
        except (ConnectionRefusedError, OSError):
            print(f"❌ Connection refused - WebSocket server not running on {host}:{port}")
            print("   Start the server with: python main.py")
            return False
            
        except Exception as e:
            print(f"❌ Error testing {endpoint}: {e}")
            return False
    
    print("\n" + "=" * 50)
    print("🎉 Manual VLLM streaming test completed!")
    print("\nTo run this test:")
    print("1. Start the WebSocket server: python main.py")
    print("2. Run this test: python manual_vllm_test.py")
    print("\nNote: This test will use VLLM if available, otherwise fallback to mock responses.")
    
    return True


async def test_vllm_configuration():
    """Test VLLM configuration and connection."""
    print("\n🔧 Testing VLLM Configuration")
    print("-" * 30)
    
    from session_manager import StreamingVLLMLLM
    from unittest.mock import Mock
    
    # Test with mock LLM
    mock_llm = Mock()
    mock_llm.vllm_api_base = "http://************:38701/v1"
    mock_llm.vllm_api_key = "EMPTY"
    mock_llm.instruction = "Test financial analysis"
    mock_llm.generate_str = lambda msg, params=None: "Mock VLLM response for: " + msg
    
    try:
        streaming_llm = StreamingVLLMLLM(mock_llm)
        print(f"✅ StreamingVLLMLLM created successfully")
        print(f"   API Base: {streaming_llm.api_base}")
        print(f"   API Key: {streaming_llm.api_key[:10]}...")
        
        # Test streaming method exists
        assert hasattr(streaming_llm, 'generate_str_streaming')
        print("✅ generate_str_streaming method available")
        
        # Test basic streaming (will fallback to mock)
        chunks = []
        
        async def collect_chunks(chunk):
            chunks.append(chunk)
        
        result = await streaming_llm.generate_str_streaming(
            "Test VLLM configuration",
            stream_callback=collect_chunks
        )
        
        print(f"✅ Streaming test completed")
        print(f"   Chunks received: {len(chunks)}")
        print(f"   Result length: {len(result) if result else 0} chars")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all manual tests."""
    print("🧪 VLLM Manual Test Suite")
    print("=" * 60)
    
    # Test configuration first
    config_success = await test_vllm_configuration()
    
    if not config_success:
        print("❌ Configuration test failed - skipping WebSocket tests")
        return False
    
    # Test WebSocket streaming
    websocket_success = await test_vllm_streaming()
    
    if config_success and websocket_success:
        print("\n🎉 All manual tests passed!")
        return True
    else:
        print("\n❌ Some tests failed - check implementation")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
