#!/usr/bin/env python3
"""
Comprehensive test to validate real VLLM streaming functionality with the actual VLLM server.
This test specifically validates streaming with http://192.168.1.54:38701/v1
"""

import asyncio
import time
import json
from typing import List, Dict, Any
from unittest.mock import Mock
from session_manager import StreamingVLLMLLM
import requests


class RealVLLMStreamingValidator:
    """Validates real VLLM streaming functionality."""
    
    def __init__(self, vllm_endpoint: str = "http://192.168.1.54:38701/v1"):
        self.vllm_endpoint = vllm_endpoint
        self.test_results = {}
        
    async def test_vllm_server_connectivity(self) -> bool:
        """Test if VLLM server is accessible."""
        print("🔍 Testing VLLM server connectivity...")
        print(f"   Endpoint: {self.vllm_endpoint}")
        
        try:
            # Test basic connectivity
            health_url = self.vllm_endpoint.replace('/v1', '/health')
            response = requests.get(health_url, timeout=5)
            
            if response.status_code == 200:
                print("✅ VLLM server is accessible")
                return True
            else:
                print(f"⚠️ VLLM server responded with status {response.status_code}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("❌ Cannot connect to VLLM server")
            print("   Make sure VLLM server is running at the specified endpoint")
            return False
        except Exception as e:
            print(f"❌ Error connecting to VLLM server: {e}")
            return False
    
    async def test_real_streaming_functionality(self) -> Dict[str, Any]:
        """Test real streaming functionality with actual VLLM server."""
        print("\n🧪 Testing Real VLLM Streaming Functionality")
        print("-" * 50)
        
        # Create mock LLM with real VLLM endpoint
        mock_llm = Mock()
        mock_llm.vllm_api_base = self.vllm_endpoint
        mock_llm.vllm_api_key = "EMPTY"
        mock_llm.instruction = "You are a financial analyst. Provide detailed analysis."
        mock_llm.generate_str = lambda msg, params=None: f"Fallback response for: {msg}"
        
        streaming_llm = StreamingVLLMLLM(mock_llm)
        
        # Test parameters
        test_message = "Analyze Apple Inc. stock performance and provide investment recommendations."
        
        # Collect streaming data
        chunks_received = []
        chunk_times = []
        start_time = time.time()
        
        async def streaming_callback(chunk: str):
            current_time = time.time()
            chunks_received.append(chunk)
            chunk_times.append(current_time - start_time)
            print(f"📦 Chunk {len(chunks_received):2d} ({current_time - start_time:.3f}s): {chunk[:80]}{'...' if len(chunk) > 80 else ''}")
        
        print(f"📤 Sending message: {test_message}")
        print("📦 Streaming response:")
        
        try:
            result = await streaming_llm.generate_str_streaming(
                message=test_message,
                stream_callback=streaming_callback,
                model="Qwen/Qwen3-32B"
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Analyze results
            is_real_streaming = self._analyze_streaming_pattern(chunks_received, chunk_times)
            
            test_results = {
                "success": True,
                "total_chunks": len(chunks_received),
                "total_time": total_time,
                "result_length": len(result) if result else 0,
                "is_real_streaming": is_real_streaming,
                "first_chunk_latency": chunk_times[0] if chunk_times else None,
                "avg_chunk_interval": self._calculate_avg_interval(chunk_times),
                "chunks_per_second": len(chunks_received) / total_time if total_time > 0 else 0,
                "contains_fallback_indicators": self._check_fallback_indicators(chunks_received),
                "result_preview": result[:200] + "..." if result and len(result) > 200 else result
            }
            
            return test_results
            
        except Exception as e:
            print(f"❌ Streaming test failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "total_chunks": len(chunks_received),
                "contains_fallback_indicators": self._check_fallback_indicators(chunks_received)
            }
    
    def _analyze_streaming_pattern(self, chunks: List[str], times: List[float]) -> bool:
        """Analyze if the streaming pattern indicates real VLLM streaming."""
        if len(chunks) < 3:
            return False
        
        # Check for real streaming indicators
        real_streaming_indicators = [
            len(chunks) > 5,  # Multiple chunks
            not any("fallback" in chunk.lower() for chunk in chunks),  # No fallback messages
            not any("failed" in chunk.lower() for chunk in chunks[:3]),  # No failure messages in first chunks
            any(len(chunk.strip()) > 10 for chunk in chunks[1:]),  # Substantial content chunks
        ]
        
        return sum(real_streaming_indicators) >= 3
    
    def _calculate_avg_interval(self, times: List[float]) -> float:
        """Calculate average interval between chunks."""
        if len(times) < 2:
            return 0.0
        
        intervals = [times[i] - times[i-1] for i in range(1, len(times))]
        return sum(intervals) / len(intervals)
    
    def _check_fallback_indicators(self, chunks: List[str]) -> bool:
        """Check if chunks contain fallback indicators."""
        fallback_keywords = ["fallback", "failed", "connection", "error", "timeout"]
        all_text = " ".join(chunks).lower()
        return any(keyword in all_text for keyword in fallback_keywords)
    
    async def test_multiple_requests(self) -> Dict[str, Any]:
        """Test multiple streaming requests to validate consistency."""
        print("\n🔄 Testing Multiple Streaming Requests")
        print("-" * 40)
        
        test_messages = [
            "Analyze Tesla's financial performance",
            "What are the key risks for Microsoft stock?",
            "Provide investment analysis for Google"
        ]
        
        results = []
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n📤 Request {i}: {message}")
            
            mock_llm = Mock()
            mock_llm.vllm_api_base = self.vllm_endpoint
            mock_llm.vllm_api_key = "EMPTY"
            mock_llm.instruction = "You are a financial analyst."
            
            streaming_llm = StreamingVLLMLLM(mock_llm)
            
            chunks = []
            start_time = time.time()
            
            async def collect_chunks(chunk):
                chunks.append(chunk)
            
            try:
                result = await streaming_llm.generate_str_streaming(
                    message=message,
                    stream_callback=collect_chunks,
                    model="Qwen/Qwen3-32B"
                )
                
                end_time = time.time()
                
                request_result = {
                    "request_id": i,
                    "message": message,
                    "chunks_received": len(chunks),
                    "duration": end_time - start_time,
                    "success": True,
                    "is_real_streaming": self._analyze_streaming_pattern(chunks, []),
                    "result_length": len(result) if result else 0
                }
                
                results.append(request_result)
                print(f"   ✅ Completed: {len(chunks)} chunks, {end_time - start_time:.2f}s")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                results.append({
                    "request_id": i,
                    "message": message,
                    "success": False,
                    "error": str(e)
                })
        
        return {
            "total_requests": len(test_messages),
            "successful_requests": sum(1 for r in results if r.get("success", False)),
            "real_streaming_count": sum(1 for r in results if r.get("is_real_streaming", False)),
            "results": results
        }
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive VLLM streaming validation."""
        print("🚀 Comprehensive VLLM Streaming Validation")
        print("=" * 60)
        print(f"Target VLLM Endpoint: {self.vllm_endpoint}")
        print("=" * 60)
        
        validation_results = {
            "endpoint": self.vllm_endpoint,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "tests": {}
        }
        
        # Test 1: Server connectivity
        connectivity_ok = await self.test_vllm_server_connectivity()
        validation_results["tests"]["connectivity"] = {
            "passed": connectivity_ok,
            "description": "VLLM server connectivity test"
        }
        
        if not connectivity_ok:
            print("\n❌ Cannot proceed with streaming tests - VLLM server not accessible")
            return validation_results
        
        # Test 2: Real streaming functionality
        streaming_results = await self.test_real_streaming_functionality()
        validation_results["tests"]["streaming"] = streaming_results
        
        # Test 3: Multiple requests
        multiple_results = await self.test_multiple_requests()
        validation_results["tests"]["multiple_requests"] = multiple_results
        
        # Overall assessment
        overall_success = (
            connectivity_ok and
            streaming_results.get("success", False) and
            streaming_results.get("is_real_streaming", False) and
            multiple_results.get("successful_requests", 0) > 0
        )
        
        validation_results["overall_success"] = overall_success
        validation_results["real_streaming_confirmed"] = streaming_results.get("is_real_streaming", False)
        
        return validation_results


async def main():
    """Run the comprehensive VLLM streaming validation."""
    validator = RealVLLMStreamingValidator()
    results = await validator.run_comprehensive_validation()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    print(f"Endpoint: {results['endpoint']}")
    print(f"Timestamp: {results['timestamp']}")
    print(f"Overall Success: {'✅' if results['overall_success'] else '❌'}")
    print(f"Real Streaming Confirmed: {'✅' if results['real_streaming_confirmed'] else '❌'}")
    
    print("\nTest Results:")
    for test_name, test_result in results["tests"].items():
        if isinstance(test_result, dict):
            if "passed" in test_result:
                status = "✅" if test_result["passed"] else "❌"
                print(f"  {status} {test_name}: {test_result.get('description', '')}")
            elif "success" in test_result:
                status = "✅" if test_result["success"] else "❌"
                print(f"  {status} {test_name}")
                if test_result.get("is_real_streaming"):
                    print(f"    🎯 Real streaming detected!")
                if test_result.get("total_chunks"):
                    print(f"    📦 Chunks: {test_result['total_chunks']}")
                if test_result.get("total_time"):
                    print(f"    ⏱️  Duration: {test_result['total_time']:.2f}s")
    
    if results.get("tests", {}).get("multiple_requests"):
        mr = results["tests"]["multiple_requests"]
        print(f"\nMultiple Requests Test:")
        print(f"  📊 {mr['successful_requests']}/{mr['total_requests']} requests successful")
        print(f"  🎯 {mr['real_streaming_count']} requests used real streaming")
    
    # Save results to file
    with open("vllm_validation_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Detailed results saved to: vllm_validation_results.json")
    
    return results["overall_success"]


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
