#!/usr/bin/env python3
"""
Discover available models on the VLLM server by trying common model names.
"""

import asyncio
from openai import OpenAI
import time


async def discover_vllm_models():
    """Discover available models by trying common model names."""
    print("🔍 Discovering Available VLLM Models")
    print("=" * 50)
    
    api_url = "http://192.168.1.54:38701/v1"
    client = OpenAI(api_key="EMPTY", base_url=api_url)
    
    # Common model names to try
    common_models = [
        # Qwen models
        "Qwen/Qwen2.5-7B-Instruct",
        "Qwen/Qwen2.5-14B-Instruct", 
        "Qwen/Qwen2.5-32B-Instruct",
        "Qwen/Qwen2.5-72B-Instruct",
        "Qwen/Qwen2-7B-Instruct",
        "Qwen/Qwen2-72B-Instruct",
        "qwen2.5-7b-instruct",
        "qwen2.5-32b-instruct",
        
        # Llama models
        "meta-llama/Llama-3.1-8B-Instruct",
        "meta-llama/Llama-3.1-70B-Instruct",
        "meta-llama/Llama-3.2-3B-Instruct",
        "llama-3.1-8b-instruct",
        "llama-3.1-70b-instruct",
        
        # Mistral models
        "mistralai/Mistral-7B-Instruct-v0.3",
        "mistralai/Mixtral-8x7B-Instruct-v0.1",
        "mistral-7b-instruct",
        
        # Generic names
        "default",
        "model",
        "chat",
        "instruct",
        
        # Try without namespace
        "Qwen2.5-7B-Instruct",
        "Qwen2.5-32B-Instruct",
        "Llama-3.1-8B-Instruct",
    ]
    
    working_models = []
    
    print(f"Testing {len(common_models)} potential model names...")
    print("(This may take a few minutes)")
    
    for i, model_name in enumerate(common_models, 1):
        print(f"\n{i:2d}. Testing: {model_name}")
        
        try:
            # Try a simple completion
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": "Hi"}
                ],
                max_tokens=10,
                temperature=0.1,
                timeout=10
            )
            
            if response.choices and response.choices[0].message.content:
                content = response.choices[0].message.content.strip()
                print(f"    ✅ WORKS! Response: {content[:50]}...")
                working_models.append(model_name)
                
                # Test streaming with this model
                print("    Testing streaming...")
                try:
                    stream = client.chat.completions.create(
                        model=model_name,
                        messages=[
                            {"role": "user", "content": "Count 1, 2, 3"}
                        ],
                        max_tokens=20,
                        temperature=0.1,
                        stream=True,
                        timeout=10
                    )
                    
                    chunks = []
                    for chunk in stream:
                        if chunk.choices and chunk.choices[0].delta.content:
                            chunks.append(chunk.choices[0].delta.content)
                            if len(chunks) >= 3:  # Limit for testing
                                break
                    
                    if chunks:
                        print(f"    ✅ Streaming works! {len(chunks)} chunks: {chunks}")
                    else:
                        print("    ⚠️ Streaming returned no chunks")
                        
                except Exception as stream_error:
                    print(f"    ❌ Streaming failed: {stream_error}")
            else:
                print("    ❌ No response content")
                
        except Exception as e:
            error_msg = str(e)
            if "does not exist" in error_msg:
                print("    ❌ Model not found")
            elif "timeout" in error_msg.lower():
                print("    ⏰ Timeout")
            elif "401" in error_msg:
                print("    🔒 Unauthorized")
            elif "500" in error_msg:
                print("    💥 Server error")
            else:
                print(f"    ❌ Error: {error_msg[:60]}...")
        
        # Small delay to avoid overwhelming the server
        await asyncio.sleep(0.1)
    
    return working_models


async def test_streaming_with_working_models(working_models):
    """Test streaming functionality with discovered working models."""
    if not working_models:
        print("\n❌ No working models found to test streaming")
        return False
    
    print(f"\n🧪 Testing Streaming with Working Models")
    print("=" * 50)
    
    from session_manager import StreamingVLLMLLM
    from unittest.mock import Mock
    
    for model_name in working_models[:2]:  # Test first 2 working models
        print(f"\n📡 Testing StreamingVLLMLLM with: {model_name}")
        print("-" * 40)
        
        # Create mock LLM
        mock_llm = Mock()
        mock_llm.vllm_api_base = "http://192.168.1.54:38701/v1"
        mock_llm.vllm_api_key = "EMPTY"
        mock_llm.instruction = "You are a helpful financial analyst."
        mock_llm.generate_str = lambda msg, params=None: f"Mock fallback for: {msg}"
        
        streaming_llm = StreamingVLLMLLM(mock_llm)
        
        # Test streaming
        chunks_received = []
        start_time = time.time()
        
        async def collect_chunks(chunk: str):
            current_time = time.time()
            chunks_received.append(chunk)
            print(f"📦 {len(chunks_received):2d} ({current_time - start_time:.3f}s): {chunk[:60]}{'...' if len(chunk) > 60 else ''}")
        
        try:
            print(f"📤 Sending: 'Analyze Apple stock briefly'")
            
            result = await streaming_llm.generate_str_streaming(
                message="Analyze Apple stock briefly",
                stream_callback=collect_chunks,
                model=model_name
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Analyze results
            has_fallback_indicators = any(
                keyword in " ".join(chunks_received).lower() 
                for keyword in ["fallback", "failed", "error", "connection"]
            )
            
            is_real_streaming = (
                len(chunks_received) > 3 and
                not has_fallback_indicators and
                any(len(chunk.strip()) > 10 for chunk in chunks_received[1:])
            )
            
            print(f"\n📊 Results for {model_name}:")
            print(f"   Chunks: {len(chunks_received)}")
            print(f"   Duration: {duration:.3f}s")
            print(f"   Result length: {len(result) if result else 0}")
            print(f"   Has fallback indicators: {'❌' if has_fallback_indicators else '✅'}")
            print(f"   Real streaming: {'✅' if is_real_streaming else '❌'}")
            
            if is_real_streaming:
                print(f"🎉 SUCCESS: Real VLLM streaming working with {model_name}!")
                return True
                
        except Exception as e:
            print(f"❌ Streaming test failed: {e}")
    
    return False


async def main():
    """Main discovery and testing function."""
    print("🚀 VLLM Model Discovery and Streaming Test")
    print("=" * 60)
    
    # Discover working models
    working_models = await discover_vllm_models()
    
    print(f"\n📋 Discovery Summary:")
    print(f"   Working models found: {len(working_models)}")
    
    if working_models:
        print("   Models that work:")
        for model in working_models:
            print(f"     ✅ {model}")
        
        # Test streaming with working models
        streaming_success = await test_streaming_with_working_models(working_models)
        
        if streaming_success:
            print(f"\n🎉 FINAL SUCCESS: Real VLLM streaming is working!")
            print(f"\n📝 Update your configuration with one of these working models:")
            for model in working_models:
                print(f"   default_model: \"{model}\"")
        else:
            print(f"\n⚠️ Models work with direct API but streaming needs debugging")
            
        return True
    else:
        print("   ❌ No working models found")
        print("\n🔧 Troubleshooting suggestions:")
        print("   1. Check if VLLM server is properly configured")
        print("   2. Verify the model is loaded on the server")
        print("   3. Check server logs for errors")
        print("   4. Try connecting to the server directly")
        
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
