#!/usr/bin/env python3
"""
VLLM Streaming Test Runner
=========================
Simple script to run the comprehensive VLLM streaming tests.

Usage:
    python run_vllm_tests.py                    # Run all tests
    python run_vllm_tests.py --connectivity     # Test connectivity only
    python run_vllm_tests.py --stage research   # Test specific stage
    python run_vllm_tests.py --pytest          # Run with pytest
"""

import asyncio
import argparse
import sys
import subprocess
from pathlib import Path

def run_pytest_tests():
    """Run tests using pytest."""
    print("🧪 Running VLLM tests with pytest...")
    
    test_file = Path(__file__).parent / "test_vllm_basic.py"
    
    # Run pytest with verbose output
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        str(test_file), 
        "-v", 
        "--tb=short",
        "--asyncio-mode=auto"
    ], capture_output=False)
    
    return result.returncode == 0

async def run_connectivity_test():
    """Run only the connectivity test."""
    print("🔍 Testing VLLM server connectivity...")
    
    from test_vllm_basic import VLLMStreamingTestSuite
    
    suite = VLLMStreamingTestSuite()
    success = await suite.verify_vllm_connectivity()
    
    if success:
        print("✅ VLLM server is accessible and ready for testing!")
    else:
        print("❌ VLLM server is not accessible. Please check:")
        print("   - Server is running at http://192.168.1.54:38701/v1")
        print("   - Network connectivity")
        print("   - Server configuration")
    
    return success

async def run_stage_test(stage: str):
    """Run test for a specific stage."""
    print(f"🎯 Testing {stage} stage...")
    
    from test_vllm_basic import VLLMStreamingTestSuite
    
    suite = VLLMStreamingTestSuite()
    
    # Verify connectivity first
    if not await suite.verify_vllm_connectivity():
        print("❌ Cannot run stage test - VLLM server not accessible")
        return False
    
    # Run specific stage test
    if stage.lower() == "research":
        success = await suite.test_research_stage_streaming()
    elif stage.lower() == "analysis":
        success = await suite.test_analysis_stage_streaming()
    elif stage.lower() == "report":
        success = await suite.test_report_stage_streaming()
    else:
        print(f"❌ Unknown stage: {stage}")
        print("   Valid stages: research, analysis, report")
        return False
    
    if success:
        print(f"✅ {stage.title()} stage test PASSED!")
    else:
        print(f"❌ {stage.title()} stage test FAILED!")
    
    return success

async def run_all_tests():
    """Run the complete test suite."""
    print("🚀 Running comprehensive VLLM streaming tests...")
    
    from test_vllm_basic import main
    
    return await main()

def main():
    parser = argparse.ArgumentParser(description="VLLM Streaming Test Runner")
    parser.add_argument("--connectivity", action="store_true", 
                       help="Test VLLM server connectivity only")
    parser.add_argument("--stage", choices=["research", "analysis", "report"],
                       help="Test specific workflow stage")
    parser.add_argument("--pytest", action="store_true",
                       help="Run tests using pytest framework")
    
    args = parser.parse_args()
    
    if args.pytest:
        success = run_pytest_tests()
    elif args.connectivity:
        success = asyncio.run(run_connectivity_test())
    elif args.stage:
        success = asyncio.run(run_stage_test(args.stage))
    else:
        success = asyncio.run(run_all_tests())
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
