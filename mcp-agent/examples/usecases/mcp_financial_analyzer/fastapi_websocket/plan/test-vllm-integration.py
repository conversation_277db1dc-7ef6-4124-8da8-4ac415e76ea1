#!/usr/bin/env python3
"""
Integration tests for VLLM streaming with WebSocket interface.
Tests the complete end-to-end flow from WebSocket to VLLM streaming.
"""

import asyncio
import json
import pytest
import websockets
import time
from typing import List, Dict, Any
from urllib.parse import quote
from unittest.mock import Mock, AsyncMock, patch

# Import test utilities
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))


class TestVLLMWebSocketIntegration:
    """Integration tests for VLLM streaming through WebSocket interface."""
    
    @pytest.fixture
    def websocket_server_config(self):
        """Configuration for WebSocket server testing."""
        return {
            'host': 'localhost',
            'port': 8000,
            'timeout': 30
        }
    
    @pytest.fixture
    def mock_vllm_streaming_response(self):
        """Mock VLLM streaming response for testing."""
        return [
            "Starting financial analysis for ",
            "the requested company. ",
            "Gathering market data... ",
            "Current stock price: $150.25. ",
            "Recent earnings show strong performance. ",
            "Analysis complete."
        ]
    
    @pytest.mark.asyncio
    async def test_research_endpoint_real_streaming(self, websocket_server_config, mock_vllm_streaming_response):
        """Test research endpoint with real VLLM streaming (mocked VLLM backend)."""
        
        # Mock the VLLM streaming to return realistic chunks
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def mock_stream_method(message, stream_callback=None, **kwargs):
                if stream_callback:
                    for chunk in mock_vllm_streaming_response:
                        await stream_callback(chunk)
                        await asyncio.sleep(0.1)  # Realistic streaming delay
                return "".join(mock_vllm_streaming_response)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = mock_stream_method
            mock_streaming_class.return_value = mock_streaming
            
            # Test WebSocket connection and streaming
            user_id = f"test_user_{int(time.time())}"
            company = "Apple Inc."
            uri = f"ws://{websocket_server_config['host']}:{websocket_server_config['port']}/ws/research/{user_id}?company={quote(company)}"
            
            try:
                async with websockets.connect(uri) as websocket:
                    # Receive welcome message
                    welcome = await websocket.recv()
                    welcome_data = json.loads(welcome)
                    assert welcome_data['type'] == 'welcome'
                    
                    # Send streaming request
                    await websocket.send(json.dumps({
                        "message": f"Research {company} with detailed analysis",
                        "streaming": True
                    }))
                    
                    # Collect streaming responses
                    stream_chunks = []
                    stream_started = False
                    stream_ended = False
                    
                    while True:
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            data = json.loads(response)
                            
                            if data["type"] == "stream_start":
                                stream_started = True
                            elif data["type"] == "stream_chunk":
                                stream_chunks.append(data["message"])
                            elif data["type"] == "stream_end":
                                stream_ended = True
                                break
                            elif data["type"] == "error":
                                pytest.fail(f"Received error: {data['message']}")
                                
                        except asyncio.TimeoutError:
                            break
                    
                    # Verify streaming behavior
                    assert stream_started, "Stream should have started"
                    assert stream_ended, "Stream should have ended"
                    assert len(stream_chunks) > 1, "Should receive multiple chunks"
                    
                    # Verify content quality
                    full_content = "".join(stream_chunks)
                    assert "financial analysis" in full_content.lower()
                    assert company.lower() in full_content.lower()
                    
            except (websockets.exceptions.ConnectionClosed, ConnectionRefusedError, OSError):
                pytest.skip("WebSocket server not running - skipping integration test")
    
    @pytest.mark.asyncio
    async def test_analyze_endpoint_vllm_streaming(self, websocket_server_config):
        """Test analyze endpoint with VLLM streaming."""
        
        analysis_chunks = [
            "Analyzing financial metrics for ",
            "the company. Key indicators: ",
            "P/E ratio: 28.5, ",
            "Revenue growth: 12.3%, ",
            "Profit margin: 23.1%. ",
            "Overall assessment: Strong buy recommendation."
        ]
        
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def mock_analyze_stream(message, stream_callback=None, **kwargs):
                if stream_callback:
                    for chunk in analysis_chunks:
                        await stream_callback(chunk)
                        await asyncio.sleep(0.08)  # Slightly faster for analysis
                return "".join(analysis_chunks)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = mock_analyze_stream
            mock_streaming_class.return_value = mock_streaming
            
            user_id = f"analyze_user_{int(time.time())}"
            company = "Tesla"
            uri = f"ws://{websocket_server_config['host']}:{websocket_server_config['port']}/ws/analyze/{user_id}?company={quote(company)}"
            
            try:
                async with websockets.connect(uri) as websocket:
                    # Skip welcome and send request
                    await websocket.recv()  # welcome
                    
                    await websocket.send(json.dumps({
                        "message": f"Perform detailed financial analysis of {company}",
                        "streaming": True
                    }))
                    
                    # Collect and verify streaming
                    chunks_received = 0
                    content_parts = []
                    
                    while True:
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                            data = json.loads(response)
                            
                            if data["type"] == "stream_chunk":
                                chunks_received += 1
                                content_parts.append(data["message"])
                            elif data["type"] == "stream_end":
                                break
                                
                        except asyncio.TimeoutError:
                            break
                    
                    # Verify analysis-specific content
                    assert chunks_received >= 3, "Should receive multiple analysis chunks"
                    full_analysis = "".join(content_parts)
                    assert "P/E ratio" in full_analysis or "financial metrics" in full_analysis
                    
            except (websockets.exceptions.ConnectionClosed, ConnectionRefusedError, OSError):
                pytest.skip("WebSocket server not running - skipping integration test")
    
    @pytest.mark.asyncio
    async def test_report_endpoint_vllm_streaming(self, websocket_server_config):
        """Test report generation endpoint with VLLM streaming."""
        
        report_chunks = [
            "# Financial Report\n\n",
            "## Executive Summary\n",
            "This report analyzes the financial performance ",
            "of the requested company. ",
            "\n\n## Key Findings\n",
            "- Strong revenue growth\n",
            "- Healthy profit margins\n",
            "- Positive market outlook\n\n",
            "## Recommendation\n",
            "Based on our analysis, we recommend a BUY rating."
        ]
        
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def mock_report_stream(message, stream_callback=None, **kwargs):
                if stream_callback:
                    for chunk in report_chunks:
                        await stream_callback(chunk)
                        await asyncio.sleep(0.12)  # Slower for report generation
                return "".join(report_chunks)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = mock_report_stream
            mock_streaming_class.return_value = mock_streaming
            
            user_id = f"report_user_{int(time.time())}"
            company = "Microsoft"
            uri = f"ws://{websocket_server_config['host']}:{websocket_server_config['port']}/ws/report/{user_id}?company={quote(company)}"
            
            try:
                async with websockets.connect(uri) as websocket:
                    await websocket.recv()  # welcome
                    
                    await websocket.send(json.dumps({
                        "message": f"Generate comprehensive financial report for {company}",
                        "streaming": True
                    }))
                    
                    # Collect report chunks
                    report_content = []
                    markdown_detected = False
                    
                    while True:
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=4.0)
                            data = json.loads(response)
                            
                            if data["type"] == "stream_chunk":
                                chunk = data["message"]
                                report_content.append(chunk)
                                if "#" in chunk or "##" in chunk:
                                    markdown_detected = True
                            elif data["type"] == "stream_end":
                                break
                                
                        except asyncio.TimeoutError:
                            break
                    
                    # Verify report structure
                    full_report = "".join(report_content)
                    assert markdown_detected, "Report should contain markdown formatting"
                    assert "Financial Report" in full_report or "Executive Summary" in full_report
                    assert len(report_content) > 3, "Report should be streamed in multiple chunks"
                    
            except (websockets.exceptions.ConnectionClosed, ConnectionRefusedError, OSError):
                pytest.skip("WebSocket server not running - skipping integration test")
    
    @pytest.mark.asyncio
    async def test_full_analysis_endpoint_vllm_streaming(self, websocket_server_config):
        """Test full analysis endpoint with VLLM streaming."""
        
        # Full analysis should have multiple phases
        full_analysis_chunks = [
            "Starting comprehensive financial analysis...\n\n",
            "Phase 1: Data Collection\n",
            "Gathering financial data from multiple sources...\n",
            "Current stock price: $145.67\n",
            "Market cap: $2.3T\n\n",
            "Phase 2: Financial Analysis\n",
            "Analyzing key financial metrics...\n",
            "Revenue: $394.3B (+8.2% YoY)\n",
            "Net income: $99.8B (+5.4% YoY)\n\n",
            "Phase 3: Report Generation\n",
            "Compiling comprehensive report...\n",
            "Final recommendation: STRONG BUY\n",
            "Target price: $165.00"
        ]
        
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def mock_full_analysis_stream(message, stream_callback=None, **kwargs):
                if stream_callback:
                    for i, chunk in enumerate(full_analysis_chunks):
                        await stream_callback(chunk)
                        # Variable delays to simulate different processing phases
                        delay = 0.15 if "Phase" in chunk else 0.08
                        await asyncio.sleep(delay)
                return "".join(full_analysis_chunks)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = mock_full_analysis_stream
            mock_streaming_class.return_value = mock_streaming
            
            user_id = f"full_analysis_user_{int(time.time())}"
            company = "Google"
            uri = f"ws://{websocket_server_config['host']}:{websocket_server_config['port']}/ws/full_analysis/{user_id}?company={quote(company)}"
            
            try:
                async with websockets.connect(uri) as websocket:  # Full analysis
                    await websocket.recv()  # welcome
                    
                    await websocket.send(json.dumps({
                        "message": f"Perform complete financial analysis of {company}",
                        "streaming": True
                    }))
                    
                    # Track phases and content
                    phases_detected = []
                    all_chunks = []
                    start_time = time.time()
                    
                    while True:
                        try:
                            response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                            data = json.loads(response)
                            
                            if data["type"] == "stream_chunk":
                                chunk = data["message"]
                                all_chunks.append(chunk)
                                
                                # Detect phases
                                if "Phase 1" in chunk:
                                    phases_detected.append("data_collection")
                                elif "Phase 2" in chunk:
                                    phases_detected.append("analysis")
                                elif "Phase 3" in chunk:
                                    phases_detected.append("report")
                                    
                            elif data["type"] == "stream_end":
                                break
                                
                        except asyncio.TimeoutError:
                            break
                    
                    end_time = time.time()
                    total_time = end_time - start_time
                    
                    # Verify comprehensive analysis
                    full_content = "".join(all_chunks)
                    assert len(phases_detected) >= 2, "Should detect multiple analysis phases"
                    assert "comprehensive" in full_content.lower() or "analysis" in full_content.lower()
                    assert total_time > 1.0, "Full analysis should take reasonable time"
                    assert len(all_chunks) > 5, "Should receive many chunks for full analysis"
                    
            except (websockets.exceptions.ConnectionClosed, ConnectionRefusedError, OSError):
                pytest.skip("WebSocket server not running - skipping integration test")


class TestVLLMStreamingPerformance:
    """Performance tests for VLLM streaming."""
    
    @pytest.mark.asyncio
    async def test_streaming_latency(self):
        """Test that VLLM streaming has low latency."""
        
        # Mock VLLM with realistic timing
        chunks = ["Quick ", "response ", "test"]
        
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def timed_stream(message, stream_callback=None, **kwargs):
                start_time = time.time()
                if stream_callback:
                    for chunk in chunks:
                        await stream_callback(chunk)
                        await asyncio.sleep(0.02)  # 20ms between chunks
                return "".join(chunks)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = timed_stream
            mock_streaming_class.return_value = mock_streaming
            
            # Measure first chunk latency
            first_chunk_time = None
            start_time = time.time()
            
            async def latency_callback(chunk):
                nonlocal first_chunk_time
                if first_chunk_time is None:
                    first_chunk_time = time.time() - start_time
            
            streaming_llm = mock_streaming_class.return_value
            await streaming_llm.generate_str_streaming(
                "Test message",
                stream_callback=latency_callback
            )
            
            # First chunk should arrive quickly
            assert first_chunk_time is not None
            assert first_chunk_time < 0.1, f"First chunk latency too high: {first_chunk_time}s"
    
    @pytest.mark.asyncio
    async def test_streaming_throughput(self):
        """Test VLLM streaming throughput."""
        
        # Generate many chunks to test throughput
        chunk_count = 50
        chunks = [f"chunk_{i} " for i in range(chunk_count)]
        
        with patch('session_manager.StreamingVLLMLLM') as mock_streaming_class:
            async def throughput_stream(message, stream_callback=None, **kwargs):
                if stream_callback:
                    for chunk in chunks:
                        await stream_callback(chunk)
                        await asyncio.sleep(0.01)  # 10ms per chunk
                return "".join(chunks)
            
            mock_streaming = Mock()
            mock_streaming.generate_str_streaming = throughput_stream
            mock_streaming_class.return_value = mock_streaming
            
            # Measure throughput
            chunks_received = 0
            start_time = time.time()
            
            async def throughput_callback(chunk):
                nonlocal chunks_received
                chunks_received += 1
            
            streaming_llm = mock_streaming_class.return_value
            await streaming_llm.generate_str_streaming(
                "Throughput test",
                stream_callback=throughput_callback
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            throughput = chunks_received / total_time
            
            # Should handle reasonable throughput
            assert chunks_received == chunk_count
            assert throughput > 10, f"Throughput too low: {throughput} chunks/sec"


if __name__ == "__main__":
    print("🔗 Running VLLM Integration Tests")
    print("=" * 60)
    
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
