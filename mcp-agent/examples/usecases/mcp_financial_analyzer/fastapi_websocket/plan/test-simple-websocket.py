#!/usr/bin/env python3
"""
Simple WebSocket connection test to debug connection issues.
"""

import asyncio
import json
import websockets
import requests


async def test_simple_connection():
    """Test simple WebSocket connection."""
    
    # First check server health
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"Health check: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"Health check failed: {e}")
        return
    
    # Test WebSocket connection
    endpoint_url = "ws://localhost:8000/ws/research/test_user?company=Apple"
    
    print(f"Connecting to: {endpoint_url}")
    
    try:
        async with websockets.connect(endpoint_url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Send a simple message
            request = {
                "message": "Hello, test message",
                "streaming": True
            }
            
            print(f"Sending: {request}")
            await websocket.send(json.dumps(request))
            
            # Wait for response
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                print(f"Received: {response}")
            except asyncio.TimeoutError:
                print("⚠️ No response received within timeout")
                
    except Exception as e:
        print(f"❌ WebSocket connection failed: {e}")
        print(f"Error type: {type(e)}")


if __name__ == "__main__":
    asyncio.run(test_simple_connection())
