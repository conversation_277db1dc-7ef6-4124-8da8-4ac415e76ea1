#!/usr/bin/env python3
"""
Test runner for VLLM streaming tests.
Runs all VLLM-related tests and provides comprehensive reporting.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def run_test_file(test_file: str, description: str) -> tuple[bool, str]:
    """Run a specific test file and return results."""
    print(f"\n🧪 Running {description}")
    print("-" * 60)
    
    try:
        # Run pytest with verbose output
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            test_file, 
            "-v", 
            "--tb=short",
            "--no-header",
            "--disable-warnings"
        ], capture_output=True, text=True, timeout=120)
        
        success = result.returncode == 0
        output = result.stdout + result.stderr
        
        if success:
            print("✅ PASSED")
        else:
            print("❌ FAILED")
            print(f"Output:\n{output}")
        
        return success, output
        
    except subprocess.TimeoutExpired:
        print("⏰ TIMEOUT - Test took too long")
        return False, "Test timeout"
    except Exception as e:
        print(f"💥 ERROR - {str(e)}")
        return False, str(e)

def main():
    """Run all VLLM streaming tests."""
    print("🚀 VLLM Streaming Test Suite")
    print("=" * 60)
    print("Following Test-Driven Development (TDD) approach")
    print("These tests define the requirements for VLLM streaming implementation")
    print()
    
    # Get the plan directory
    plan_dir = Path(__file__).parent
    
    # Define test files and descriptions
    test_files = [
        ("test-vllm-streaming.py", "Core VLLM Streaming Tests"),
        ("test-vllm-integration.py", "VLLM WebSocket Integration Tests"),
    ]
    
    # Track results
    results = []
    start_time = time.time()
    
    # Run each test file
    for test_file, description in test_files:
        test_path = plan_dir / test_file
        if test_path.exists():
            success, output = run_test_file(str(test_path), description)
            results.append((test_file, description, success, output))
        else:
            print(f"⚠️ Test file not found: {test_file}")
            results.append((test_file, description, False, "File not found"))
    
    # Summary
    end_time = time.time()
    total_time = end_time - start_time
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, _, success, _ in results if success)
    total = len(results)
    
    for test_file, description, success, _ in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {description}")
    
    print(f"\nResults: {passed}/{total} test files passed")
    print(f"Total time: {total_time:.2f} seconds")
    
    if passed == total:
        print("\n🎉 All tests passed! Ready for implementation.")
        return 0
    else:
        print(f"\n⚠️ {total - passed} test file(s) failed. Implementation needed.")
        print("\nNext steps:")
        print("1. Implement StreamingVLLMLLM class")
        print("2. Update session_manager.py to use VLLM")
        print("3. Add VLLM configuration support")
        print("4. Re-run tests to verify implementation")
        return 1

if __name__ == "__main__":
    sys.exit(main())
