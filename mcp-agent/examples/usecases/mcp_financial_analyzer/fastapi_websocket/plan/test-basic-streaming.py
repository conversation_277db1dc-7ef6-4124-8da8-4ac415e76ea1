#!/usr/bin/env python3
"""
Test basic streaming functionality to ensure WebSocket connection works properly.
"""

import asyncio
import json
import websockets
import requests


async def test_basic_streaming():
    """Test basic WebSocket streaming without MCP integration."""
    
    # Check server health
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"✅ Server health: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not available: {e}")
        return False
    
    # Test WebSocket connection
    endpoint_url = "ws://localhost:8000/ws/research/test_user?company=Apple"
    
    print(f"🔗 Connecting to: {endpoint_url}")
    
    try:
        async with websockets.connect(endpoint_url) as websocket:
            print("✅ WebSocket connected successfully!")
            
            # Send a simple message
            request = {
                "message": "Give me a brief analysis of Apple Inc.",
                "streaming": True
            }
            
            print(f"📤 Sending: {request['message']}")
            await websocket.send(json.dumps(request))
            
            # Collect messages
            messages = []
            timeout_count = 0
            max_timeout = 30  # 30 second timeout
            
            while timeout_count < max_timeout:
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    msg_data = json.loads(message)
                    messages.append(msg_data)
                    
                    msg_type = msg_data.get("type", "unknown")
                    
                    if msg_type == "system":
                        print(f"🔔 System: {msg_data.get('message', '')}")
                    elif msg_type == "stream_chunk":
                        content = msg_data.get("message", "")
                        if content.strip():  # Only print non-empty chunks
                            print(f"📦 Chunk: {content[:80]}{'...' if len(content) > 80 else ''}")
                    elif msg_type == "mcp_tool_stream":
                        tool_data = msg_data.get("data", {})
                        print(f"🔧 MCP Tool: {tool_data.get('type')} - {tool_data.get('tool_name')}")
                    elif msg_type == "stream_end":
                        print(f"🏁 Stream ended: {msg_data.get('message', '')}")
                        break
                    elif msg_type == "error":
                        print(f"❌ Error: {msg_data.get('message', '')}")
                        break
                    else:
                        print(f"📨 {msg_type}: {str(msg_data)[:100]}...")
                        
                except asyncio.TimeoutError:
                    timeout_count += 1
                    if timeout_count % 10 == 0:  # Print every 10 seconds
                        print(f"⏳ Waiting... ({timeout_count}s)")
                    continue
                except websockets.exceptions.ConnectionClosed:
                    print("🔌 WebSocket connection closed")
                    break
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break
            
            print(f"\n📊 Results:")
            print(f"   Total messages: {len(messages)}")
            
            if messages:
                message_types = {}
                for msg in messages:
                    msg_type = msg.get('type', 'unknown')
                    message_types[msg_type] = message_types.get(msg_type, 0) + 1
                
                print(f"   Message types:")
                for msg_type, count in message_types.items():
                    print(f"     {msg_type}: {count}")
                
                # Check for expected functionality
                has_streaming = "stream_chunk" in message_types
                has_completion = "stream_end" in message_types
                has_mcp_tools = "mcp_tool_stream" in message_types
                
                print(f"\n✅ Basic streaming: {has_streaming}")
                print(f"✅ Stream completion: {has_completion}")
                print(f"🔧 MCP tool streaming: {has_mcp_tools}")
                
                if has_streaming and has_completion:
                    print("\n🎉 SUCCESS: Basic streaming is working!")
                    return True
                else:
                    print("\n⚠️ Basic streaming issues detected")
                    return False
            else:
                print("❌ No messages received")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")
        return False


async def main():
    """Run the basic streaming test."""
    print("🚀 Testing Basic WebSocket Streaming")
    print("=" * 50)
    
    success = await test_basic_streaming()
    
    if success:
        print("\n🎉 Basic streaming test passed!")
    else:
        print("\n❌ Basic streaming test failed!")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
