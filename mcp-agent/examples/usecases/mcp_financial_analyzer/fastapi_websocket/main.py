import json
import logging
import logging.config
from datetime import datetime
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import uvicorn
from contextlib import asynccontextmanager

from session_manager import FinancialSessionManager, SessionType

# Configure detailed debug logging
logging_config = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(funcName)s() - %(message)s'
        },
        'simple': {
            'format': '%(asctime)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'debug_file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'logs/main_debug.log',
            'formatter': 'detailed',
            'mode': 'a'
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        }
    },
    'loggers': {
        '': {  # root logger
            'handlers': ['debug_file', 'console'],
            'level': 'DEBUG',
            'propagate': False
        },
        'uvicorn': {
            'handlers': ['debug_file'],
            'level': 'DEBUG',
            'propagate': False
        },
        'fastapi': {
            'handlers': ['debug_file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}

logging.config.dictConfig(logging_config)
logger = logging.getLogger(__name__)

# Log startup
logger.info("="*60)
logger.info("MCP Financial Analyzer WebSocket Server Starting")
logger.info(f"Startup time: {datetime.now().isoformat()}")
logger.info("="*60)

# Global session manager
session_manager = FinancialSessionManager()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Startup and shutdown events for the FastAPI application."""
    # Startup
    logger.info("FastAPI lifespan: Starting up application")
    logger.debug("Initializing session manager...")
    try:
        await session_manager.initialize()
        logger.info("Session manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize session manager: {e}", exc_info=True)
        raise
    
    yield
    
    # Shutdown
    logger.info("FastAPI lifespan: Shutting down application")
    logger.debug("Cleaning up session manager...")
    try:
        await session_manager.cleanup()
        logger.info("Session manager cleanup completed")
    except Exception as e:
        logger.error(f"Error during session manager cleanup: {e}", exc_info=True)


app = FastAPI(title="MCP Financial Analyzer WebSocket Server", lifespan=lifespan)


@app.get("/")
async def get():
    """Serve a comprehensive HTML page for testing financial analysis WebSocket connections."""
    logger.debug("Serving HTML test page for WebSocket interface")
    return HTMLResponse("""
<!DOCTYPE html>
<html>
<head>
    <title>MCP Financial Analyzer WebSocket Interface</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .endpoint-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint-title { font-size: 18px; font-weight: bold; color: #333; margin-bottom: 10px; }
        .controls { display: flex; gap: 10px; align-items: center; margin-bottom: 15px; flex-wrap: wrap; }
        .controls input, .controls button { padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; }
        .controls button { background: #007bff; color: white; cursor: pointer; }
        .controls button:hover { background: #0056b3; }
        .controls button:disabled { background: #ccc; cursor: not-allowed; }
        .status { padding: 5px 10px; border-radius: 3px; font-weight: bold; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .messages { border: 1px solid #ccc; height: 300px; overflow-y: auto; padding: 10px; margin: 10px 0; background: #fafafa; }
        .message { margin: 5px 0; padding: 8px; border-radius: 4px; }
        .message.user { background: #e3f2fd; border-left: 4px solid #2196f3; }
        .message.system { background: #f3e5f5; border-left: 4px solid #9c27b0; }
        .message.result { background: #e8f5e8; border-left: 4px solid #4caf50; }
        .message.progress { background: #fff3e0; border-left: 4px solid #ff9800; }
        .message.error { background: #ffebee; border-left: 4px solid #f44336; }
        .timestamp { font-size: 0.8em; color: #666; }
        .company-input { min-width: 200px; }
        .user-id-input { min-width: 150px; }

        /* Streaming message styles */
        .message.streaming {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            position: relative;
        }
        .stream-content {
            white-space: pre-wrap;
            font-family: monospace;
            margin-bottom: 5px;
        }
        .stream-indicator {
            font-size: 0.9em;
            color: #ff9800;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        .streaming .stream-indicator::after {
            content: '';
            width: 12px;
            height: 12px;
            border: 2px solid #ff9800;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏦 MCP Financial Analyzer WebSocket Interface</h1>
            <p>Test the financial analysis WebSocket endpoints with real-time communication</p>
        </div>

        <!-- Research Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📊 Financial Research</div>
            <div class="controls">
                <input type="text" id="research-user-id" placeholder="User ID" class="user-id-input" value="user123">
                <input type="text" id="research-company" placeholder="Company Name" class="company-input" value="Apple Inc.">
                <button onclick="connectResearch()">Connect</button>
                <button onclick="disconnectResearch()" disabled id="research-disconnect">Disconnect</button>
                <span id="research-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="research-message" placeholder="Enter your financial research query..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendResearchMessage()" disabled id="research-send">Send</button>
            <div id="research-messages" class="messages"></div>
        </div>

        <!-- Analysis Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🔍 Financial Analysis</div>
            <div class="controls">
                <input type="text" id="analyze-user-id" placeholder="User ID" class="user-id-input" value="user456">
                <input type="text" id="analyze-company" placeholder="Company Name" class="company-input" value="Tesla Inc.">
                <button onclick="connectAnalyze()">Connect</button>
                <button onclick="disconnectAnalyze()" disabled id="analyze-disconnect">Disconnect</button>
                <span id="analyze-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="analyze-message" placeholder="Enter your analysis request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendAnalyzeMessage()" disabled id="analyze-send">Send</button>
            <div id="analyze-messages" class="messages"></div>
        </div>

        <!-- Report Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">📈 Financial Report</div>
            <div class="controls">
                <input type="text" id="report-user-id" placeholder="User ID" class="user-id-input" value="user789">
                <input type="text" id="report-company" placeholder="Company Name" class="company-input" value="Microsoft Corp.">
                <button onclick="connectReport()">Connect</button>
                <button onclick="disconnectReport()" disabled id="report-disconnect">Disconnect</button>
                <span id="report-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="report-message" placeholder="Enter your report request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendReportMessage()" disabled id="report-send">Send</button>
            <div id="report-messages" class="messages"></div>
        </div>

        <!-- Full Analysis Endpoint -->
        <div class="endpoint-section">
            <div class="endpoint-title">🎯 Full Analysis</div>
            <div class="controls">
                <input type="text" id="full-user-id" placeholder="User ID" class="user-id-input" value="user999">
                <input type="text" id="full-company" placeholder="Company Name" class="company-input" value="Amazon.com Inc.">
                <button onclick="connectFull()">Connect</button>
                <button onclick="disconnectFull()" disabled id="full-disconnect">Disconnect</button>
                <span id="full-status" class="status disconnected">Disconnected</span>
            </div>
            <input type="text" id="full-message" placeholder="Enter your full analysis request..." style="width: 70%; margin-right: 10px;">
            <button onclick="sendFullMessage()" disabled id="full-send">Send</button>
            <div id="full-messages" class="messages"></div>
        </div>
    </div>

    <script>
        let researchWs = null;
        let analyzeWs = null;
        let reportWs = null;
        let fullWs = null;

        function addMessage(containerId, type, message, timestamp = new Date()) {
            const container = document.getElementById(containerId);
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div>${message}</div>
                <div class="timestamp">${timestamp.toLocaleTimeString()}</div>
            `;
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(statusId, connected) {
            const status = document.getElementById(statusId);
            status.textContent = connected ? 'Connected' : 'Disconnected';
            status.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function updateButtons(prefix, connected) {
            document.querySelector(`button[onclick="connect${prefix}()"]`).disabled = connected;
            document.getElementById(`${prefix.toLowerCase()}-disconnect`).disabled = !connected;
            document.getElementById(`${prefix.toLowerCase()}-send`).disabled = !connected;
        }

        // Streaming message handling
        let streamingMessages = {}; // Track streaming messages by container ID

        function handleStreamingMessage(containerId, data) {
            switch(data.type) {
                case 'stream_start':
                    // Start a new streaming message
                    const streamDiv = document.createElement('div');
                    streamDiv.className = 'message assistant streaming';
                    streamDiv.innerHTML = `
                        <div class="stream-content"></div>
                        <div class="stream-indicator">🔄 Streaming...</div>
                        <div class="timestamp">${new Date().toLocaleTimeString()}</div>
                    `;

                    const container = document.getElementById(containerId);
                    container.appendChild(streamDiv);
                    container.scrollTop = container.scrollHeight;

                    // Store reference for streaming updates
                    streamingMessages[containerId] = {
                        element: streamDiv,
                        content: streamDiv.querySelector('.stream-content'),
                        indicator: streamDiv.querySelector('.stream-indicator'),
                        fullText: ''
                    };

                    addMessage(containerId, 'system', data.message);
                    break;

                case 'stream_chunk':
                    // Append chunk to streaming message
                    if (streamingMessages[containerId]) {
                        const streaming = streamingMessages[containerId];
                        streaming.fullText += data.message;
                        streaming.content.textContent = streaming.fullText;

                        // Auto-scroll to bottom
                        const container = document.getElementById(containerId);
                        container.scrollTop = container.scrollHeight;
                    }
                    break;

                case 'stream_end':
                    // Finalize streaming message
                    if (streamingMessages[containerId]) {
                        const streaming = streamingMessages[containerId];
                        streaming.indicator.textContent = '✅ Complete';
                        streaming.indicator.style.color = '#4caf50';
                        streaming.element.classList.remove('streaming');

                        // Clean up reference
                        delete streamingMessages[containerId];
                    }

                    addMessage(containerId, 'system', data.message);
                    break;

                default:
                    // Handle non-streaming messages normally
                    addMessage(containerId, data.type, data.message);
                    break;
            }
        }

        // Research WebSocket
        function connectResearch() {
            const userId = document.getElementById('research-user-id').value;
            const company = document.getElementById('research-company').value;
            researchWs = new WebSocket(`ws://localhost:8000/ws/research/${userId}?company=${encodeURIComponent(company)}`);
            
            researchWs.onopen = function() {
                addMessage('research-messages', 'system', 'Connected to Research endpoint');
                updateStatus('research-status', true);
                updateButtons('Research', true);
            };
            
            researchWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('research-messages', data);
            };
            
            researchWs.onclose = function() {
                addMessage('research-messages', 'system', 'Disconnected from Research endpoint');
                updateStatus('research-status', false);
                updateButtons('Research', false);
            };
        }

        function disconnectResearch() {
            if (researchWs) {
                researchWs.close();
            }
        }

        function sendResearchMessage() {
            const message = document.getElementById('research-message').value;
            if (researchWs && message) {
                researchWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('research-messages', 'user', message);
                document.getElementById('research-message').value = '';
            }
        }

        // Analysis WebSocket
        function connectAnalyze() {
            const userId = document.getElementById('analyze-user-id').value;
            const company = document.getElementById('analyze-company').value;
            analyzeWs = new WebSocket(`ws://localhost:8000/ws/analyze/${userId}?company=${encodeURIComponent(company)}`);
            
            analyzeWs.onopen = function() {
                addMessage('analyze-messages', 'system', 'Connected to Analysis endpoint');
                updateStatus('analyze-status', true);
                updateButtons('Analyze', true);
            };
            
            analyzeWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('analyze-messages', data);
            };
            
            analyzeWs.onclose = function() {
                addMessage('analyze-messages', 'system', 'Disconnected from Analysis endpoint');
                updateStatus('analyze-status', false);
                updateButtons('Analyze', false);
            };
        }

        function disconnectAnalyze() {
            if (analyzeWs) {
                analyzeWs.close();
            }
        }

        function sendAnalyzeMessage() {
            const message = document.getElementById('analyze-message').value;
            if (analyzeWs && message) {
                analyzeWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('analyze-messages', 'user', message);
                document.getElementById('analyze-message').value = '';
            }
        }

        // Report WebSocket
        function connectReport() {
            const userId = document.getElementById('report-user-id').value;
            const company = document.getElementById('report-company').value;
            reportWs = new WebSocket(`ws://localhost:8000/ws/report/${userId}?company=${encodeURIComponent(company)}`);
            
            reportWs.onopen = function() {
                addMessage('report-messages', 'system', 'Connected to Report endpoint');
                updateStatus('report-status', true);
                updateButtons('Report', true);
            };
            
            reportWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('report-messages', data);
            };
            
            reportWs.onclose = function() {
                addMessage('report-messages', 'system', 'Disconnected from Report endpoint');
                updateStatus('report-status', false);
                updateButtons('Report', false);
            };
        }

        function disconnectReport() {
            if (reportWs) {
                reportWs.close();
            }
        }

        function sendReportMessage() {
            const message = document.getElementById('report-message').value;
            if (reportWs && message) {
                reportWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('report-messages', 'user', message);
                document.getElementById('report-message').value = '';
            }
        }

        // Full Analysis WebSocket
        function connectFull() {
            const userId = document.getElementById('full-user-id').value;
            const company = document.getElementById('full-company').value;
            fullWs = new WebSocket(`ws://localhost:8000/ws/full_analysis/${userId}?company=${encodeURIComponent(company)}`);
            
            fullWs.onopen = function() {
                addMessage('full-messages', 'system', 'Connected to Full Analysis endpoint');
                updateStatus('full-status', true);
                updateButtons('Full', true);
            };
            
            fullWs.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleStreamingMessage('full-messages', data);
            };
            
            fullWs.onclose = function() {
                addMessage('full-messages', 'system', 'Disconnected from Full Analysis endpoint');
                updateStatus('full-status', false);
                updateButtons('Full', false);
            };
        }

        function disconnectFull() {
            if (fullWs) {
                fullWs.close();
            }
        }

        function sendFullMessage() {
            const message = document.getElementById('full-message').value;
            if (fullWs && message) {
                fullWs.send(JSON.stringify({message: message, streaming: true}));
                addMessage('full-messages', 'user', message);
                document.getElementById('full-message').value = '';
            }
        }

        // Enter key support for message inputs
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                if (e.target.id === 'research-message') sendResearchMessage();
                else if (e.target.id === 'analyze-message') sendAnalyzeMessage();
                else if (e.target.id === 'report-message') sendReportMessage();
                else if (e.target.id === 'full-message') sendFullMessage();
            }
        });
    </script>
</body>
</html>
    """)


async def handle_websocket_connection(websocket: WebSocket, user_id: str, session_type: SessionType, company_name: str):
    """Common WebSocket connection handler for all endpoints."""
    connection_id = f"{user_id}:{session_type.value}:{company_name}"
    logger.info(f"WebSocket connection attempt - ID: {connection_id}")
    
    try:
        logger.debug(f"Accepting WebSocket connection for {connection_id}")
        await websocket.accept()
        logger.info(f"WebSocket connection accepted for {connection_id}")
    except Exception as e:
        logger.error(f"Failed to accept WebSocket connection for {connection_id}: {e}", exc_info=True)
        return

    try:
        # Get or create user session
        logger.debug(f"Getting or creating session for {connection_id}")
        user_session = await session_manager.get_or_create_session(user_id, session_type, company_name)
        logger.info(f"Session created/retrieved - ID: {user_session.session_id} for connection: {connection_id}")

        # Send welcome message
        welcome_msg = {
            "type": "system",
            "message": f"Welcome to {session_type.value} analysis for {company_name}! Session ID: {user_session.session_id}",
            "user_id": user_id,
        }
        logger.debug(f"Sending welcome message to {connection_id}: {welcome_msg}")
        await websocket.send_text(json.dumps(welcome_msg))

        message_count = 0
        while True:
            try:
                # Receive message from client
                logger.debug(f"Waiting for message from {connection_id}")
                data = await websocket.receive_text()
                message_count += 1
                logger.debug(f"Received message #{message_count} from {connection_id}: {data[:100]}{'...' if len(data) > 100 else ''}")
                
                try:
                    message_data = json.loads(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Invalid JSON received from {connection_id}: {e}")
                    await websocket.send_text(
                        json.dumps({"type": "error", "message": "Invalid JSON format"})
                    )
                    continue

                user_message = message_data.get("message", "")
                use_streaming = message_data.get("streaming", True)  # Default to streaming

                if not user_message:
                    logger.debug(f"Empty message received from {connection_id}, skipping")
                    continue

                logger.info(f"Processing message from {connection_id}: '{user_message}', streaming: {use_streaming}")

                if use_streaming:
                    # Send stream start message
                    stream_start_msg = {
                        "type": "stream_start",
                        "message": f"Starting {session_type.value} analysis...",
                        "user_id": user_id,
                    }
                    logger.debug(f"Sending stream start message to {connection_id}")
                    await websocket.send_text(json.dumps(stream_start_msg))

                    # Process message with streaming
                    logger.debug(f"Processing message with streaming through session {user_session.session_id}")
                    start_time = datetime.now()

                    try:
                        # Define streaming callback for LLM chunks
                        async def stream_callback(chunk: str):
                            chunk_msg = {
                                "type": "stream_chunk",
                                "message": chunk,
                                "user_id": user_id,
                            }
                            await websocket.send_text(json.dumps(chunk_msg))

                        # Define streaming callback for MCP tool calls
                        async def mcp_stream_callback(tool_message: dict):
                            mcp_msg = {
                                "type": "mcp_tool_stream",
                                "data": tool_message,
                                "user_id": user_id,
                            }
                            await websocket.send_text(json.dumps(mcp_msg))

                        # Set MCP streaming callback on the session
                        user_session.set_mcp_stream_callback(mcp_stream_callback)

                        # Process message through appropriate agent with streaming
                        response = await user_session.process_message_streaming(user_message, stream_callback)
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"Streaming response completed for {connection_id} in {processing_time:.2f}s")
                        logger.debug(f"Response length: {len(response)} characters")

                        # Send stream end message
                        stream_end_msg = {
                            "type": "stream_end",
                            "message": "Analysis complete",
                            "full_response": response,
                            "user_id": user_id,
                        }
                        logger.debug(f"Sending stream end message to {connection_id}")
                        await websocket.send_text(json.dumps(stream_end_msg))
                        logger.info(f"Stream ended for {connection_id}")

                    except Exception as e:
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.error(f"Error processing streaming message for {connection_id} after {processing_time:.2f}s: {e}", exc_info=True)
                        error_msg = {
                            "type": "error",
                            "message": f"Error processing your request: {str(e)}",
                        }
                        await websocket.send_text(json.dumps(error_msg))
                else:
                    # Non-streaming mode (original behavior)
                    # Send progress message
                    progress_msg = {
                        "type": "progress",
                        "message": f"Processing {session_type.value} request...",
                    }
                    logger.debug(f"Sending progress message to {connection_id}")
                    await websocket.send_text(json.dumps(progress_msg))

                    # Process message through appropriate agent
                    logger.debug(f"Processing message through session {user_session.session_id}")
                    start_time = datetime.now()
                    try:
                        response = await user_session.process_message(user_message)
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.info(f"Message processed successfully for {connection_id} in {processing_time:.2f}s")
                        logger.debug(f"Response length: {len(response)} characters")
                    except Exception as e:
                        processing_time = (datetime.now() - start_time).total_seconds()
                        logger.error(f"Error processing message for {connection_id} after {processing_time:.2f}s: {e}", exc_info=True)
                        response = f"Error processing your request: {str(e)}"

                    # Send response back to client
                    response_msg = {
                        "type": "result",
                        "message": response,
                        "user_id": user_id,
                    }
                    logger.debug(f"Sending response to {connection_id}: {len(response)} characters")
                    await websocket.send_text(json.dumps(response_msg))
                    logger.info(f"Response sent successfully to {connection_id}")

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for {connection_id}")
                break
            except Exception as e:
                logger.error(f"Error in message loop for {connection_id}: {e}", exc_info=True)
                try:
                    error_msg = {"type": "error", "message": f"An error occurred: {str(e)}"}
                    await websocket.send_text(json.dumps(error_msg))
                except Exception as send_error:
                    logger.error(f"Failed to send error message to {connection_id}: {send_error}")
                # Continue the loop instead of breaking, unless it's a connection issue
                if "connection" in str(e).lower():
                    logger.warning(f"Connection-related error for {connection_id}, breaking loop")
                    break

    except Exception as e:
        logger.error(f"Session error for {connection_id}: {e}", exc_info=True)
        try:
            await websocket.send_text(
                json.dumps({"type": "error", "message": f"Session error: {str(e)}"})
            )
        except Exception as send_error:
            logger.error(f"Failed to send session error message to {connection_id}: {send_error}")
    finally:
        # Clean up session if needed
        logger.info(f"Cleaning up session for {connection_id}")
        try:
            await session_manager.cleanup_session(user_id, session_type)
            logger.debug(f"Session cleanup completed for {connection_id}")
        except Exception as e:
            logger.error(f"Error during session cleanup for {connection_id}: {e}", exc_info=True)


@app.websocket("/ws/research/{user_id}")
async def research_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial research functionality."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Research endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.RESEARCH, company)


@app.websocket("/ws/analyze/{user_id}")
async def analyze_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for financial analysis operations."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Analyze endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.ANALYZE, company)


@app.websocket("/ws/report/{user_id}")
async def report_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for generating financial reports."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Report endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.REPORT, company)


@app.websocket("/ws/full_analysis/{user_id}")
async def full_analysis_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for comprehensive end-to-end financial analysis."""
    company = websocket.query_params.get("company", "Apple Inc.")
    logger.info(f"Full analysis endpoint accessed - User: {user_id}, Company: {company}")
    await handle_websocket_connection(websocket, user_id, SessionType.FULL_ANALYSIS, company)


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    logger.debug("Health check endpoint called")
    active_sessions = len(session_manager.sessions)
    logger.debug(f"Active sessions count: {active_sessions}")
    
    health_data = {
        "status": "healthy", 
        "active_sessions": active_sessions,
        "endpoints": ["research", "analyze", "report", "full_analysis"],
        "timestamp": datetime.now().isoformat()
    }
    logger.debug(f"Returning health check data: {health_data}")
    return health_data


@app.get("/sessions")
async def list_sessions():
    """List active sessions."""
    logger.debug("Listing active sessions endpoint called")
    sessions_info = {}
    
    for session_key, session in session_manager.sessions.items():
        session_info = {
            "user_id": session.user_id,
            "session_type": session.session_type.value,
            "company_name": session.company_name,
            "created_at": session.created_at.isoformat(),
            "last_activity": session.last_activity.isoformat(),
            "message_count": len(session.message_history),
        }
        sessions_info[session_key] = session_info
        logger.debug(f"Session {session_key}: {session_info}")
    
    result = {
        "active_sessions": sessions_info,
        "total_sessions": len(session_manager.sessions),
    }
    logger.info(f"Returning {len(sessions_info)} active sessions")
    return result


@app.get("/sessions/{user_id}/{session_type}")
async def get_session_info(user_id: str, session_type: str):
    """Get information about a specific session."""
    logger.debug(f"Getting session info for user: {user_id}, type: {session_type}")
    
    try:
        session_type_enum = SessionType(session_type)
        session_info = session_manager.get_session_info(user_id, session_type_enum)
        
        if session_info:
            logger.debug(f"Found session info for {user_id}:{session_type}")
            return session_info
        else:
            logger.warning(f"Session not found for {user_id}:{session_type}")
            return {"error": "Session not found"}
            
    except ValueError as e:
        logger.error(f"Invalid session type '{session_type}' for user {user_id}: {e}")
        return {"error": f"Invalid session type: {session_type}"}


if __name__ == "__main__":
    logger.info("Starting MCP Financial Analyzer WebSocket Server")
    logger.info("Server configuration: host=0.0.0.0, port=8000")
    uvicorn.run(app, host="0.0.0.0", port=8000)
