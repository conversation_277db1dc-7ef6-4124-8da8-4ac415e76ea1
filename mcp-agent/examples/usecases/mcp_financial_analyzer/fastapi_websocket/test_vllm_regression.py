#!/usr/bin/env python3
"""
Regression tests to ensure VLLM implementation doesn't break existing functionality.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from session_manager import (
    FinancialSession, 
    FinancialSessionManager, 
    SessionType,
    StreamingVLLMLLM,
    StreamingOpenAILLM
)


class TestVLLMRegression:
    """Regression tests for VLLM implementation."""
    
    @pytest.mark.asyncio
    async def test_session_manager_initialization(self):
        """Test that session manager still initializes correctly with VLLM."""
        session_manager = FinancialSessionManager()
        await session_manager.initialize()
        
        # Should initialize without errors
        assert session_manager is not None
        assert hasattr(session_manager, 'sessions')
        
        # Cleanup
        await session_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_all_session_types_create_successfully(self):
        """Test that all session types can be created with VLLM."""
        session_manager = FinancialSessionManager()
        await session_manager.initialize()
        
        session_types = [
            SessionType.RESEARCH,
            SessionType.ANALYZE,
            SessionType.REPORT,
            SessionType.FULL_ANALYSIS
        ]
        
        for session_type in session_types:
            with patch('session_manager.create_research_agent') as mock_research, \
                 patch('session_manager.create_analyst_agent') as mock_analyst, \
                 patch('session_manager.create_report_writer') as mock_report:
                
                # Mock all agent types
                mock_agent = Mock()
                mock_agent.__aenter__ = AsyncMock(return_value=mock_agent)
                mock_agent.__aexit__ = AsyncMock(return_value=None)
                mock_agent.attach_llm = AsyncMock(return_value=Mock())
                
                mock_research.return_value = mock_agent
                mock_analyst.return_value = mock_agent
                mock_report.return_value = mock_agent
                
                # Create session
                session = await session_manager.get_or_create_session(
                    f"test_user_{session_type.value}",
                    session_type,
                    "Test Company"
                )
                
                # Should create successfully
                assert session is not None
                assert session.session_type == session_type
                assert isinstance(session.streaming_llm, StreamingVLLMLLM)
        
        await session_manager.cleanup()
    
    @pytest.mark.asyncio
    async def test_streaming_interface_compatibility(self):
        """Test that StreamingVLLMLLM has same interface as StreamingOpenAILLM."""
        mock_llm = Mock()
        mock_llm.vllm_api_base = "http://test:8000/v1"
        mock_llm.vllm_api_key = "test-key"
        mock_llm.generate_str = AsyncMock(return_value="Test response")
        mock_llm.instruction = "Test instruction"
        
        # Create both streaming LLM types
        vllm_streaming = StreamingVLLMLLM(mock_llm)
        openai_streaming = StreamingOpenAILLM(mock_llm)
        
        # Both should have the same interface
        assert hasattr(vllm_streaming, 'generate_str_streaming')
        assert hasattr(openai_streaming, 'generate_str_streaming')
        
        # Both should be callable with same parameters
        chunks_vllm = []
        chunks_openai = []
        
        async def vllm_callback(chunk):
            chunks_vllm.append(chunk)
            
        async def openai_callback(chunk):
            chunks_openai.append(chunk)
        
        # Test both implementations
        result_vllm = await vllm_streaming.generate_str_streaming(
            message="Test message",
            stream_callback=vllm_callback
        )
        
        result_openai = await openai_streaming.generate_str_streaming(
            message="Test message", 
            stream_callback=openai_callback
        )
        
        # Both should return results
        assert result_vllm is not None
        assert result_openai is not None
        
        # Both should have called callbacks
        assert len(chunks_vllm) > 0
        assert len(chunks_openai) > 0
    
    @pytest.mark.asyncio
    async def test_session_message_processing_still_works(self):
        """Test that session message processing works with VLLM."""
        with patch('session_manager.create_research_agent') as mock_create_agent:
            mock_agent = Mock()
            mock_agent.__aenter__ = AsyncMock(return_value=mock_agent)
            mock_agent.__aexit__ = AsyncMock(return_value=None)
            
            mock_llm = Mock()
            mock_llm.vllm_api_base = "http://test:8000/v1"
            mock_llm.vllm_api_key = "test-key"
            mock_llm.generate_str = AsyncMock(return_value="Analysis complete")
            mock_llm.instruction = "Test instruction"
            
            mock_agent.attach_llm = AsyncMock(return_value=mock_llm)
            mock_create_agent.return_value = mock_agent
            
            # Create session
            session = FinancialSession("test_user", "test_session", SessionType.RESEARCH)
            await session.initialize("Test Company")
            
            # Test message processing
            chunks_received = []
            
            async def collect_chunks(chunk):
                chunks_received.append(chunk)
            
            result = await session.process_message_streaming(
                "Analyze Test Company",
                collect_chunks
            )
            
            # Should process successfully
            assert result is not None
            assert len(chunks_received) > 0
            
            # Cleanup
            await session.cleanup()
    
    @pytest.mark.asyncio
    async def test_error_handling_still_works(self):
        """Test that error handling works correctly with VLLM."""
        mock_llm = Mock()
        mock_llm.vllm_api_base = "http://test:8000/v1"
        mock_llm.vllm_api_key = "test-key"
        mock_llm.generate_str = AsyncMock(side_effect=Exception("Test error"))
        mock_llm.instruction = "Test instruction"
        
        streaming_llm = StreamingVLLMLLM(mock_llm)
        
        error_chunks = []
        
        async def error_callback(chunk):
            error_chunks.append(chunk)
        
        # Should handle error gracefully
        result = await streaming_llm.generate_str_streaming(
            message="Test message",
            stream_callback=error_callback
        )
        
        # Should have error handling
        assert result is not None  # Should return some error message
        assert len(error_chunks) > 0  # Should have sent error info to callback
        
        # Check that error information was communicated
        all_chunks = "".join(error_chunks)
        assert "error" in all_chunks.lower() or "failed" in all_chunks.lower()
    
    @pytest.mark.asyncio
    async def test_configuration_backward_compatibility(self):
        """Test that existing configuration still works."""
        # Test that we can create StreamingVLLMLLM with minimal configuration
        mock_llm = Mock()
        
        # Should work even without explicit VLLM configuration
        streaming_llm = StreamingVLLMLLM(mock_llm)
        
        # Should have default values
        assert streaming_llm.api_base is not None
        assert streaming_llm.api_key is not None
        
        # Should be able to override configuration
        custom_streaming_llm = StreamingVLLMLLM(
            mock_llm,
            api_base="http://custom:8000/v1",
            api_key="custom-key"
        )
        
        assert custom_streaming_llm.api_base == "http://custom:8000/v1"
        assert custom_streaming_llm.api_key == "custom-key"


if __name__ == "__main__":
    print("🔄 Running VLLM Regression Tests")
    print("=" * 50)
    
    # Run the tests
    pytest.main([__file__, "-v", "--tb=short"])
